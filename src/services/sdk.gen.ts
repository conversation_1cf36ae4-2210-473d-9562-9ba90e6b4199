// This file is auto-generated by @hey-api/openapi-ts

import { type Options as ClientOptions, type TDataShape, type Client, urlSearchParamsBodySerializer } from './client';
import type { LoginApiV1AuthTokenPostData, LoginApiV1AuthTokenPostResponses, LoginApiV1AuthTokenPostErrors, ReadUsersMeApiV1AuthMeGetData, ReadUsersMeApiV1AuthMeGetResponses, GenerateImageCodeApiV1AuthGenerateImageCodePostData, GenerateImageCodeApiV1AuthGenerateImageCodePostResponses, GenerateImageCodeApiV1AuthGenerateImageCodePostErrors, SendSmsCodeApiV1AuthSendSmsCodePostData, SendSmsCodeApiV1AuthSendSmsCodePostResponses, SendSmsCodeApiV1AuthSendSmsCodePostErrors, LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostData, LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostResponses, LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostErrors, LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostData, LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostResponses, LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostErrors, CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostData, CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostResponses, GetUsersApiV1UsersGetData, GetUsersApiV1UsersGetResponses, GetUsersApiV1UsersGetErrors, CreateUserApiV1UsersPostData, CreateUserApiV1UsersPostResponses, CreateUserApiV1UsersPostErrors, DeleteUserApiV1UsersUserIdDeleteData, DeleteUserApiV1UsersUserIdDeleteResponses, DeleteUserApiV1UsersUserIdDeleteErrors, GetUserApiV1UsersUserIdGetData, GetUserApiV1UsersUserIdGetResponses, GetUserApiV1UsersUserIdGetErrors, UpdateUserApiV1UsersUserIdPutData, UpdateUserApiV1UsersUserIdPutResponses, UpdateUserApiV1UsersUserIdPutErrors, RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostData, RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostResponses, RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostErrors, ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostData, ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostResponses, ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostErrors, GetMyFactoriesApiV1FactoryManagementMyFactoriesGetData, GetMyFactoriesApiV1FactoryManagementMyFactoriesGetResponses, GetPendingRequestsApiV1FactoryManagementPendingRequestsGetData, GetPendingRequestsApiV1FactoryManagementPendingRequestsGetResponses, GetFactoryMembersApiV1FactoryManagementMembersGetData, GetFactoryMembersApiV1FactoryManagementMembersGetResponses, UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutData, UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutResponses, UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutErrors, SuspendUserApiV1FactoryManagementUserUserIdSuspendPostData, SuspendUserApiV1FactoryManagementUserUserIdSuspendPostResponses, SuspendUserApiV1FactoryManagementUserUserIdSuspendPostErrors, ResignFromFactoryApiV1FactoryManagementResignPostData, ResignFromFactoryApiV1FactoryManagementResignPostResponses, GetSessionStatusApiV1SessionStatusGetData, GetSessionStatusApiV1SessionStatusGetResponses, GetSessionStatusApiV1SessionStatusGetErrors, GetAvailableFactoriesApiV1SessionAvailableFactoriesGetData, GetAvailableFactoriesApiV1SessionAvailableFactoriesGetResponses, GetAvailableFactoriesApiV1SessionAvailableFactoriesGetErrors, SwitchFactoryContextApiV1SessionSwitchFactoryPostData, SwitchFactoryContextApiV1SessionSwitchFactoryPostResponses, SwitchFactoryContextApiV1SessionSwitchFactoryPostErrors, RefreshFactoryContextApiV1SessionRefreshContextPostData, RefreshFactoryContextApiV1SessionRefreshContextPostResponses, RefreshFactoryContextApiV1SessionRefreshContextPostErrors, ExtendSessionApiV1SessionExtendPostData, ExtendSessionApiV1SessionExtendPostResponses, ExtendSessionApiV1SessionExtendPostErrors, LogoutApiV1SessionLogoutDeleteData, LogoutApiV1SessionLogoutDeleteResponses, LogoutApiV1SessionLogoutDeleteErrors, GetPermissionTreeApiV1PermissionsTreeGetData, GetPermissionTreeApiV1PermissionsTreeGetResponses, GetAllPermissionsApiV1PermissionsListGetData, GetAllPermissionsApiV1PermissionsListGetResponses, GetAllRolesApiV1RolesGetData, GetAllRolesApiV1RolesGetResponses, CreateRoleApiV1RolesPostData, CreateRoleApiV1RolesPostResponses, CreateRoleApiV1RolesPostErrors, GetActiveRolesApiV1RolesActiveGetData, GetActiveRolesApiV1RolesActiveGetResponses, GetRolesSummaryApiV1RolesSummaryGetData, GetRolesSummaryApiV1RolesSummaryGetResponses, DeleteRoleApiV1RolesRoleIdDeleteData, DeleteRoleApiV1RolesRoleIdDeleteResponses, DeleteRoleApiV1RolesRoleIdDeleteErrors, GetRoleByIdApiV1RolesRoleIdGetData, GetRoleByIdApiV1RolesRoleIdGetResponses, GetRoleByIdApiV1RolesRoleIdGetErrors, UpdateRoleApiV1RolesRoleIdPutData, UpdateRoleApiV1RolesRoleIdPutResponses, UpdateRoleApiV1RolesRoleIdPutErrors, RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteData, RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteResponses, RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteErrors, GetRolePermissionsApiV1RolesRoleIdPermissionsGetData, GetRolePermissionsApiV1RolesRoleIdPermissionsGetResponses, GetRolePermissionsApiV1RolesRoleIdPermissionsGetErrors, AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostData, AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostResponses, AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostErrors, GetFactoryUserListApiV1UserManagementUserListGetData, GetFactoryUserListApiV1UserManagementUserListGetResponses, GetAvailableUsersApiV1UserManagementAvailableUsersGetData, GetAvailableUsersApiV1UserManagementAvailableUsersGetResponses, GetAvailableUsersApiV1UserManagementAvailableUsersGetErrors, AddUsersToFactoryApiV1UserManagementAddUsersPostData, AddUsersToFactoryApiV1UserManagementAddUsersPostResponses, AddUsersToFactoryApiV1UserManagementAddUsersPostErrors, BindUserRoleApiV1UserManagementBindRolesPostData, BindUserRoleApiV1UserManagementBindRolesPostResponses, BindUserRoleApiV1UserManagementBindRolesPostErrors, SuspendUserInFactoryApiV1UserManagementSuspendPostData, SuspendUserInFactoryApiV1UserManagementSuspendPostResponses, SuspendUserInFactoryApiV1UserManagementSuspendPostErrors, RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteData, RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteResponses, RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteErrors, CreateUserWithFactoryApiV1UserManagementCreateUserPostData, CreateUserWithFactoryApiV1UserManagementCreateUserPostResponses, CreateUserWithFactoryApiV1UserManagementCreateUserPostErrors, GetAllSkillsApiV1SkillsGetData, GetAllSkillsApiV1SkillsGetResponses, GetAllSkillsApiV1SkillsGetErrors, CreateSkillApiV1SkillsPostData, CreateSkillApiV1SkillsPostResponses, CreateSkillApiV1SkillsPostErrors, GetActiveSkillsApiV1SkillsActiveGetData, GetActiveSkillsApiV1SkillsActiveGetResponses, DeleteSkillApiV1SkillsSkillIdDeleteData, DeleteSkillApiV1SkillsSkillIdDeleteResponses, DeleteSkillApiV1SkillsSkillIdDeleteErrors, GetSkillByIdApiV1SkillsSkillIdGetData, GetSkillByIdApiV1SkillsSkillIdGetResponses, GetSkillByIdApiV1SkillsSkillIdGetErrors, UpdateSkillApiV1SkillsSkillIdPutData, UpdateSkillApiV1SkillsSkillIdPutResponses, UpdateSkillApiV1SkillsSkillIdPutErrors, GetUserSkillsApiV1SkillsUserUserIdSkillsGetData, GetUserSkillsApiV1SkillsUserUserIdSkillsGetResponses, GetUserSkillsApiV1SkillsUserUserIdSkillsGetErrors, AssignSkillsToUserApiV1SkillsAssignPostData, AssignSkillsToUserApiV1SkillsAssignPostResponses, AssignSkillsToUserApiV1SkillsAssignPostErrors, ModifySkillProficiencyApiV1SkillsModifyProficiencyPutData, ModifySkillProficiencyApiV1SkillsModifyProficiencyPutResponses, ModifySkillProficiencyApiV1SkillsModifyProficiencyPutErrors, CertifyUserSkillApiV1SkillsCertifyPostData, CertifyUserSkillApiV1SkillsCertifyPostResponses, CertifyUserSkillApiV1SkillsCertifyPostErrors, RemoveUserSkillApiV1SkillsRemoveDeleteData, RemoveUserSkillApiV1SkillsRemoveDeleteResponses, RemoveUserSkillApiV1SkillsRemoveDeleteErrors, GetAllCraftsApiV1CraftsGetData, GetAllCraftsApiV1CraftsGetResponses, GetAllCraftsApiV1CraftsGetErrors, CreateCraftApiV1CraftsPostData, CreateCraftApiV1CraftsPostResponses, CreateCraftApiV1CraftsPostErrors, GetEnabledCraftsApiV1CraftsEnabledGetData, GetEnabledCraftsApiV1CraftsEnabledGetResponses, DeleteCraftApiV1CraftsCraftIdDeleteData, DeleteCraftApiV1CraftsCraftIdDeleteResponses, DeleteCraftApiV1CraftsCraftIdDeleteErrors, GetCraftByIdApiV1CraftsCraftIdGetData, GetCraftByIdApiV1CraftsCraftIdGetResponses, GetCraftByIdApiV1CraftsCraftIdGetErrors, UpdateCraftApiV1CraftsCraftIdPutData, UpdateCraftApiV1CraftsCraftIdPutResponses, UpdateCraftApiV1CraftsCraftIdPutErrors, GetCraftByCodeApiV1CraftsCodeCraftCodeGetData, GetCraftByCodeApiV1CraftsCodeCraftCodeGetResponses, GetCraftByCodeApiV1CraftsCodeCraftCodeGetErrors, CreateCraftRouteApiV1CraftsRoutesPostData, CreateCraftRouteApiV1CraftsRoutesPostResponses, CreateCraftRouteApiV1CraftsRoutesPostErrors, CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostData, CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostResponses, CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostErrors, GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetData, GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetResponses, GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetErrors, GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetData, GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetResponses, GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetErrors, DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteData, DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteResponses, DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteErrors, UpdateCraftRouteApiV1CraftsRoutesRouteIdPutData, UpdateCraftRouteApiV1CraftsRoutesRouteIdPutResponses, UpdateCraftRouteApiV1CraftsRoutesRouteIdPutErrors, ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostData, ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostResponses, ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostErrors, GetAllOrdersApiV1OrdersGetData, GetAllOrdersApiV1OrdersGetResponses, GetAllOrdersApiV1OrdersGetErrors, CreateOrderApiV1OrdersPostData, CreateOrderApiV1OrdersPostResponses, CreateOrderApiV1OrdersPostErrors, GetOrderByOrderNoMainApiV1OrdersOrderNoGetData, GetOrderByOrderNoMainApiV1OrdersOrderNoGetResponses, GetOrderByOrderNoMainApiV1OrdersOrderNoGetErrors, DeleteOrderApiV1OrdersOrderIdDeleteData, DeleteOrderApiV1OrdersOrderIdDeleteResponses, DeleteOrderApiV1OrdersOrderIdDeleteErrors, UpdateOrderApiV1OrdersOrderIdPutData, UpdateOrderApiV1OrdersOrderIdPutResponses, UpdateOrderApiV1OrdersOrderIdPutErrors, UpdateOrderStatusApiV1OrdersOrderIdStatusPutData, UpdateOrderStatusApiV1OrdersOrderIdStatusPutResponses, UpdateOrderStatusApiV1OrdersOrderIdStatusPutErrors, StartOrderApiV1OrdersOrderNoStartPostData, StartOrderApiV1OrdersOrderNoStartPostResponses, StartOrderApiV1OrdersOrderNoStartPostErrors, UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutData, UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutResponses, UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutErrors, UpdateOrderAmountApiV1OrdersOrderIdAmountPutData, UpdateOrderAmountApiV1OrdersOrderIdAmountPutResponses, UpdateOrderAmountApiV1OrdersOrderIdAmountPutErrors, AddOrderLinesApiV1OrdersOrderLinesBulkPostData, AddOrderLinesApiV1OrdersOrderLinesBulkPostResponses, AddOrderLinesApiV1OrdersOrderLinesBulkPostErrors, UpdateProductionApiV1OrdersProductionPutData, UpdateProductionApiV1OrdersProductionPutResponses, UpdateProductionApiV1OrdersProductionPutErrors, GetOrderStatisticsApiV1OrdersStatisticsSummaryGetData, GetOrderStatisticsApiV1OrdersStatisticsSummaryGetResponses, GetOrderStatisticsApiV1OrdersStatisticsSummaryGetErrors, GetDashboardDataApiV1OrdersDashboardDataGetData, GetDashboardDataApiV1OrdersDashboardDataGetResponses, GetDashboardDataApiV1OrdersDashboardDataGetErrors, SearchOrderPartsApiV1OrderPartsGetData, SearchOrderPartsApiV1OrderPartsGetResponses, SearchOrderPartsApiV1OrderPartsGetErrors, CreateOrderPartApiV1OrderPartsPostData, CreateOrderPartApiV1OrderPartsPostResponses, CreateOrderPartApiV1OrderPartsPostErrors, GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetData, GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetResponses, GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetErrors, DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteData, DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteResponses, DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteErrors, GetOrderPartByIdApiV1OrderPartsOrderPartIdGetData, GetOrderPartByIdApiV1OrderPartsOrderPartIdGetResponses, GetOrderPartByIdApiV1OrderPartsOrderPartIdGetErrors, UpdateOrderPartApiV1OrderPartsOrderPartIdPutData, UpdateOrderPartApiV1OrderPartsOrderPartIdPutResponses, UpdateOrderPartApiV1OrderPartsOrderPartIdPutErrors, GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetData, GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetResponses, GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetErrors, UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutData, UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutResponses, UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutErrors, BulkCreateOrderPartsApiV1OrderPartsBulkPostData, BulkCreateOrderPartsApiV1OrderPartsBulkPostResponses, BulkCreateOrderPartsApiV1OrderPartsBulkPostErrors, GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetData, GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetResponses, GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetErrors, SearchOrderBundlesApiV1OrderBundlesGetData, SearchOrderBundlesApiV1OrderBundlesGetResponses, SearchOrderBundlesApiV1OrderBundlesGetErrors, CreateOrderBundleApiV1OrderBundlesPostData, CreateOrderBundleApiV1OrderBundlesPostResponses, CreateOrderBundleApiV1OrderBundlesPostErrors, GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetData, GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetResponses, GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetErrors, GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetData, GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetResponses, GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetErrors, DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteData, DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteResponses, DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteErrors, GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetData, GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetResponses, GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetErrors, UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutData, UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutResponses, UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutErrors, UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutData, UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutResponses, UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutErrors, UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutData, UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutResponses, UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutErrors, BulkCreateOrderBundlesApiV1OrderBundlesBulkPostData, BulkCreateOrderBundlesApiV1OrderBundlesBulkPostResponses, BulkCreateOrderBundlesApiV1OrderBundlesBulkPostErrors, GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetData, GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetResponses, GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetErrors, GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetData, GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetResponses, GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetErrors, CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostData, CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostResponses, CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostErrors, GetOrderCraftByIdApiV1CraftsOrderCraftIdGetData, GetOrderCraftByIdApiV1CraftsOrderCraftIdGetResponses, GetOrderCraftByIdApiV1CraftsOrderCraftIdGetErrors, UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutData, UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutResponses, UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutErrors, UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutData, UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutResponses, UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutErrors, GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetData, GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetResponses, GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetErrors, GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetData, GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetResponses, GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetErrors, GetOrderCraftStatisticsApiV1StatisticsGetData, GetOrderCraftStatisticsApiV1StatisticsGetResponses, GetOrderCraftStatisticsApiV1StatisticsGetErrors, CreateCraftInstanceApiV1CraftInstancesPostData, CreateCraftInstanceApiV1CraftInstancesPostResponses, CreateCraftInstanceApiV1CraftInstancesPostErrors, QrScanRegisterApiV1CraftInstancesQrScanPostData, QrScanRegisterApiV1CraftInstancesQrScanPostResponses, QrScanRegisterApiV1CraftInstancesQrScanPostErrors, SearchCraftInstancesApiV1CraftInstancesSearchGetData, SearchCraftInstancesApiV1CraftInstancesSearchGetResponses, SearchCraftInstancesApiV1CraftInstancesSearchGetErrors, GetCraftInstanceApiV1CraftInstancesInstanceIdGetData, GetCraftInstanceApiV1CraftInstancesInstanceIdGetResponses, GetCraftInstanceApiV1CraftInstancesInstanceIdGetErrors, VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostData, VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostResponses, VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostErrors, RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostData, RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostResponses, RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostErrors, GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetData, GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetResponses, GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetErrors, GetAvailableRegistrationDataData, GetAvailableRegistrationDataResponses, GetAvailableRegistrationDataErrors, GetRegistrationSummaryData, GetRegistrationSummaryResponses, GetRegistrationSummaryErrors, RootGetData, RootGetResponses, HealthCheckHealthGetData, HealthCheckHealthGetResponses } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Login
 * Login and get access token with session context.
 */
export const loginApiV1AuthTokenPost = <ThrowOnError extends boolean = false>(options: Options<LoginApiV1AuthTokenPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<LoginApiV1AuthTokenPostResponses, LoginApiV1AuthTokenPostErrors, ThrowOnError>({
        ...urlSearchParamsBodySerializer,
        responseType: 'json',
        url: '/api/v1/auth/token',
        ...options,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            ...options.headers
        }
    });
};

/**
 * Read Users Me
 * Get current user information.
 */
export const readUsersMeApiV1AuthMeGet = <ThrowOnError extends boolean = false>(options?: Options<ReadUsersMeApiV1AuthMeGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ReadUsersMeApiV1AuthMeGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/auth/me',
        ...options
    });
};

/**
 * Generate Image Code
 * Generate image validation code.
 */
export const generateImageCodeApiV1AuthGenerateImageCodePost = <ThrowOnError extends boolean = false>(options: Options<GenerateImageCodeApiV1AuthGenerateImageCodePostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GenerateImageCodeApiV1AuthGenerateImageCodePostResponses, GenerateImageCodeApiV1AuthGenerateImageCodePostErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/auth/generate-image-code',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Send Sms Code
 * Send SMS validation code.
 */
export const sendSmsCodeApiV1AuthSendSmsCodePost = <ThrowOnError extends boolean = false>(options: Options<SendSmsCodeApiV1AuthSendSmsCodePostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SendSmsCodeApiV1AuthSendSmsCodePostResponses, SendSmsCodeApiV1AuthSendSmsCodePostErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/auth/send-sms-code',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Login With Phone Password
 * Login with phone + password + image validation code.
 */
export const loginWithPhonePasswordApiV1AuthLoginPhonePasswordPost = <ThrowOnError extends boolean = false>(options: Options<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostResponses, LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/auth/login-phone-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Login With Phone Sms
 * Login with phone + SMS code + image validation code.
 */
export const loginWithPhoneSmsApiV1AuthLoginPhoneSmsPost = <ThrowOnError extends boolean = false>(options: Options<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostResponses, LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/auth/login-phone-sms',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Cleanup Expired Codes
 * Clean up expired validation codes (admin endpoint).
 */
export const cleanupExpiredCodesApiV1AuthCleanupExpiredCodesPost = <ThrowOnError extends boolean = false>(options?: Options<CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostResponses, unknown, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/auth/cleanup-expired-codes',
        ...options
    });
};

/**
 * Get Users
 * Get all users.
 */
export const getUsersApiV1UsersGet = <ThrowOnError extends boolean = false>(options?: Options<GetUsersApiV1UsersGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetUsersApiV1UsersGetResponses, GetUsersApiV1UsersGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/users/',
        ...options
    });
};

/**
 * Create User
 * Create a new user.
 */
export const createUserApiV1UsersPost = <ThrowOnError extends boolean = false>(options: Options<CreateUserApiV1UsersPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateUserApiV1UsersPostResponses, CreateUserApiV1UsersPostErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/users/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete User
 * Delete user.
 */
export const deleteUserApiV1UsersUserIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteUserApiV1UsersUserIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteUserApiV1UsersUserIdDeleteResponses, DeleteUserApiV1UsersUserIdDeleteErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/users/{user_id}',
        ...options
    });
};

/**
 * Get User
 * Get user by ID.
 */
export const getUserApiV1UsersUserIdGet = <ThrowOnError extends boolean = false>(options: Options<GetUserApiV1UsersUserIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetUserApiV1UsersUserIdGetResponses, GetUserApiV1UsersUserIdGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/users/{user_id}',
        ...options
    });
};

/**
 * Update User
 * Update user.
 */
export const updateUserApiV1UsersUserIdPut = <ThrowOnError extends boolean = false>(options: Options<UpdateUserApiV1UsersUserIdPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateUserApiV1UsersUserIdPutResponses, UpdateUserApiV1UsersUserIdPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/users/{user_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Request To Join Factory
 * Request to join a factory.
 */
export const requestToJoinFactoryApiV1FactoryManagementJoinRequestPost = <ThrowOnError extends boolean = false>(options: Options<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostResponses, RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/factory-management/join-request',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Approve Or Reject Request
 * Approve or reject a factory join request (managers only).
 */
export const approveOrRejectRequestApiV1FactoryManagementApproveRequestPost = <ThrowOnError extends boolean = false>(options: Options<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostResponses, ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/factory-management/approve-request',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get My Factories
 * Get current user's factory relationships.
 */
export const getMyFactoriesApiV1FactoryManagementMyFactoriesGet = <ThrowOnError extends boolean = false>(options?: Options<GetMyFactoriesApiV1FactoryManagementMyFactoriesGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetMyFactoriesApiV1FactoryManagementMyFactoriesGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/factory-management/my-factories',
        ...options
    });
};

/**
 * Get Pending Requests
 * Get pending join requests for current factory (managers only).
 */
export const getPendingRequestsApiV1FactoryManagementPendingRequestsGet = <ThrowOnError extends boolean = false>(options?: Options<GetPendingRequestsApiV1FactoryManagementPendingRequestsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetPendingRequestsApiV1FactoryManagementPendingRequestsGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/factory-management/pending-requests',
        ...options
    });
};

/**
 * Get Factory Members
 * Get all members of current factory.
 */
export const getFactoryMembersApiV1FactoryManagementMembersGet = <ThrowOnError extends boolean = false>(options?: Options<GetFactoryMembersApiV1FactoryManagementMembersGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetFactoryMembersApiV1FactoryManagementMembersGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/factory-management/members',
        ...options
    });
};

/**
 * Update User Role
 * Update user's role in current factory (managers only).
 */
export const updateUserRoleApiV1FactoryManagementUserUserIdRolePut = <ThrowOnError extends boolean = false>(options: Options<UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutResponses, UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/factory-management/user/{user_id}/role',
        ...options
    });
};

/**
 * Suspend User
 * Suspend user from current factory (managers only).
 */
export const suspendUserApiV1FactoryManagementUserUserIdSuspendPost = <ThrowOnError extends boolean = false>(options: Options<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SuspendUserApiV1FactoryManagementUserUserIdSuspendPostResponses, SuspendUserApiV1FactoryManagementUserUserIdSuspendPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/factory-management/user/{user_id}/suspend',
        ...options
    });
};

/**
 * Resign From Factory
 * Resign from current factory.
 */
export const resignFromFactoryApiV1FactoryManagementResignPost = <ThrowOnError extends boolean = false>(options?: Options<ResignFromFactoryApiV1FactoryManagementResignPostData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<ResignFromFactoryApiV1FactoryManagementResignPostResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/factory-management/resign',
        ...options
    });
};

/**
 * Get Session Status
 * Get current session status and factory context.
 */
export const getSessionStatusApiV1SessionStatusGet = <ThrowOnError extends boolean = false>(options?: Options<GetSessionStatusApiV1SessionStatusGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetSessionStatusApiV1SessionStatusGetResponses, GetSessionStatusApiV1SessionStatusGetErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/session/status',
        ...options
    });
};

/**
 * Get Available Factories
 * Get list of factories user can switch to.
 */
export const getAvailableFactoriesApiV1SessionAvailableFactoriesGet = <ThrowOnError extends boolean = false>(options?: Options<GetAvailableFactoriesApiV1SessionAvailableFactoriesGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAvailableFactoriesApiV1SessionAvailableFactoriesGetResponses, GetAvailableFactoriesApiV1SessionAvailableFactoriesGetErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/session/available-factories',
        ...options
    });
};

/**
 * Switch Factory Context
 * Switch user's factory context.
 */
export const switchFactoryContextApiV1SessionSwitchFactoryPost = <ThrowOnError extends boolean = false>(options: Options<SwitchFactoryContextApiV1SessionSwitchFactoryPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SwitchFactoryContextApiV1SessionSwitchFactoryPostResponses, SwitchFactoryContextApiV1SessionSwitchFactoryPostErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/session/switch-factory',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Refresh Factory Context
 * Refresh factory context with latest data.
 */
export const refreshFactoryContextApiV1SessionRefreshContextPost = <ThrowOnError extends boolean = false>(options?: Options<RefreshFactoryContextApiV1SessionRefreshContextPostData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<RefreshFactoryContextApiV1SessionRefreshContextPostResponses, RefreshFactoryContextApiV1SessionRefreshContextPostErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/session/refresh-context',
        ...options
    });
};

/**
 * Extend Session
 * Extend session expiration.
 */
export const extendSessionApiV1SessionExtendPost = <ThrowOnError extends boolean = false>(options?: Options<ExtendSessionApiV1SessionExtendPostData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<ExtendSessionApiV1SessionExtendPostResponses, ExtendSessionApiV1SessionExtendPostErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/session/extend',
        ...options
    });
};

/**
 * Logout
 * Logout and destroy session.
 */
export const logoutApiV1SessionLogoutDelete = <ThrowOnError extends boolean = false>(options?: Options<LogoutApiV1SessionLogoutDeleteData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<LogoutApiV1SessionLogoutDeleteResponses, LogoutApiV1SessionLogoutDeleteErrors, ThrowOnError>({
        responseType: 'json',
        url: '/api/v1/session/logout',
        ...options
    });
};

/**
 * Get Permission Tree
 * Get hierarchical permission tree.
 */
export const getPermissionTreeApiV1PermissionsTreeGet = <ThrowOnError extends boolean = false>(options?: Options<GetPermissionTreeApiV1PermissionsTreeGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetPermissionTreeApiV1PermissionsTreeGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/permissions/tree',
        ...options
    });
};

/**
 * Get All Permissions
 * Get all permissions as a flat list.
 */
export const getAllPermissionsApiV1PermissionsListGet = <ThrowOnError extends boolean = false>(options?: Options<GetAllPermissionsApiV1PermissionsListGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAllPermissionsApiV1PermissionsListGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/permissions/list',
        ...options
    });
};

/**
 * Get All Roles
 * Get all roles.
 */
export const getAllRolesApiV1RolesGet = <ThrowOnError extends boolean = false>(options?: Options<GetAllRolesApiV1RolesGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAllRolesApiV1RolesGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/roles/',
        ...options
    });
};

/**
 * Create Role
 * Create a new role.
 */
export const createRoleApiV1RolesPost = <ThrowOnError extends boolean = false>(options: Options<CreateRoleApiV1RolesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateRoleApiV1RolesPostResponses, CreateRoleApiV1RolesPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/roles/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Active Roles
 * Get all active roles.
 */
export const getActiveRolesApiV1RolesActiveGet = <ThrowOnError extends boolean = false>(options?: Options<GetActiveRolesApiV1RolesActiveGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetActiveRolesApiV1RolesActiveGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/roles/active',
        ...options
    });
};

/**
 * Get Roles Summary
 * Get summary of all roles without full permission details.
 */
export const getRolesSummaryApiV1RolesSummaryGet = <ThrowOnError extends boolean = false>(options?: Options<GetRolesSummaryApiV1RolesSummaryGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetRolesSummaryApiV1RolesSummaryGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/roles/summary',
        ...options
    });
};

/**
 * Delete Role
 * Delete role.
 */
export const deleteRoleApiV1RolesRoleIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteRoleApiV1RolesRoleIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteRoleApiV1RolesRoleIdDeleteResponses, DeleteRoleApiV1RolesRoleIdDeleteErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/roles/{role_id}',
        ...options
    });
};

/**
 * Get Role By Id
 * Get role by ID.
 */
export const getRoleByIdApiV1RolesRoleIdGet = <ThrowOnError extends boolean = false>(options: Options<GetRoleByIdApiV1RolesRoleIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetRoleByIdApiV1RolesRoleIdGetResponses, GetRoleByIdApiV1RolesRoleIdGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/roles/{role_id}',
        ...options
    });
};

/**
 * Update Role
 * Update role.
 */
export const updateRoleApiV1RolesRoleIdPut = <ThrowOnError extends boolean = false>(options: Options<UpdateRoleApiV1RolesRoleIdPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateRoleApiV1RolesRoleIdPutResponses, UpdateRoleApiV1RolesRoleIdPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/roles/{role_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Remove Permissions From Role
 * Remove permissions from role.
 */
export const removePermissionsFromRoleApiV1RolesRoleIdPermissionsDelete = <ThrowOnError extends boolean = false>(options: Options<RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteResponses, RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/roles/{role_id}/permissions',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Role Permissions
 * Get permissions assigned to a role.
 */
export const getRolePermissionsApiV1RolesRoleIdPermissionsGet = <ThrowOnError extends boolean = false>(options: Options<GetRolePermissionsApiV1RolesRoleIdPermissionsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetRolePermissionsApiV1RolesRoleIdPermissionsGetResponses, GetRolePermissionsApiV1RolesRoleIdPermissionsGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/roles/{role_id}/permissions',
        ...options
    });
};

/**
 * Assign Permissions To Role
 * Assign permissions to role.
 */
export const assignPermissionsToRoleApiV1RolesRoleIdPermissionsPost = <ThrowOnError extends boolean = false>(options: Options<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostResponses, AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/roles/{role_id}/permissions',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Factory User List
 * Get list of all users in the current factory.
 */
export const getFactoryUserListApiV1UserManagementUserListGet = <ThrowOnError extends boolean = false>(options?: Options<GetFactoryUserListApiV1UserManagementUserListGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetFactoryUserListApiV1UserManagementUserListGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/user-management/user-list',
        ...options
    });
};

/**
 * Get Available Users
 * Get list of users available to add to the factory.
 */
export const getAvailableUsersApiV1UserManagementAvailableUsersGet = <ThrowOnError extends boolean = false>(options?: Options<GetAvailableUsersApiV1UserManagementAvailableUsersGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAvailableUsersApiV1UserManagementAvailableUsersGetResponses, GetAvailableUsersApiV1UserManagementAvailableUsersGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/user-management/available-users',
        ...options
    });
};

/**
 * Add Users To Factory
 * Add multiple users to the current factory.
 */
export const addUsersToFactoryApiV1UserManagementAddUsersPost = <ThrowOnError extends boolean = false>(options: Options<AddUsersToFactoryApiV1UserManagementAddUsersPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AddUsersToFactoryApiV1UserManagementAddUsersPostResponses, AddUsersToFactoryApiV1UserManagementAddUsersPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/user-management/add-users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Bind User Role
 * Bind system role to a user.
 */
export const bindUserRoleApiV1UserManagementBindRolesPost = <ThrowOnError extends boolean = false>(options: Options<BindUserRoleApiV1UserManagementBindRolesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<BindUserRoleApiV1UserManagementBindRolesPostResponses, BindUserRoleApiV1UserManagementBindRolesPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/user-management/bind-roles',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Suspend User In Factory
 * Suspend a user in the current factory.
 */
export const suspendUserInFactoryApiV1UserManagementSuspendPost = <ThrowOnError extends boolean = false>(options: Options<SuspendUserInFactoryApiV1UserManagementSuspendPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SuspendUserInFactoryApiV1UserManagementSuspendPostResponses, SuspendUserInFactoryApiV1UserManagementSuspendPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/user-management/suspend',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Remove User From Factory
 * Remove a user from the current factory.
 */
export const removeUserFromFactoryApiV1UserManagementRemoveUserDelete = <ThrowOnError extends boolean = false>(options: Options<RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteResponses, RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/user-management/remove-user',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create User With Factory
 * Create new user or add existing user to factory with skills and complete information.
 */
export const createUserWithFactoryApiV1UserManagementCreateUserPost = <ThrowOnError extends boolean = false>(options: Options<CreateUserWithFactoryApiV1UserManagementCreateUserPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateUserWithFactoryApiV1UserManagementCreateUserPostResponses, CreateUserWithFactoryApiV1UserManagementCreateUserPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/user-management/create-user',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get All Skills
 * Get all skills with optional filters.
 */
export const getAllSkillsApiV1SkillsGet = <ThrowOnError extends boolean = false>(options?: Options<GetAllSkillsApiV1SkillsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAllSkillsApiV1SkillsGetResponses, GetAllSkillsApiV1SkillsGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/',
        ...options
    });
};

/**
 * Create Skill
 * Create a new skill.
 */
export const createSkillApiV1SkillsPost = <ThrowOnError extends boolean = false>(options: Options<CreateSkillApiV1SkillsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateSkillApiV1SkillsPostResponses, CreateSkillApiV1SkillsPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Active Skills
 * Get all active skills.
 */
export const getActiveSkillsApiV1SkillsActiveGet = <ThrowOnError extends boolean = false>(options?: Options<GetActiveSkillsApiV1SkillsActiveGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetActiveSkillsApiV1SkillsActiveGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/active',
        ...options
    });
};

/**
 * Delete Skill
 * Delete skill.
 */
export const deleteSkillApiV1SkillsSkillIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteSkillApiV1SkillsSkillIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteSkillApiV1SkillsSkillIdDeleteResponses, DeleteSkillApiV1SkillsSkillIdDeleteErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/{skill_id}',
        ...options
    });
};

/**
 * Get Skill By Id
 * Get skill by ID.
 */
export const getSkillByIdApiV1SkillsSkillIdGet = <ThrowOnError extends boolean = false>(options: Options<GetSkillByIdApiV1SkillsSkillIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetSkillByIdApiV1SkillsSkillIdGetResponses, GetSkillByIdApiV1SkillsSkillIdGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/{skill_id}',
        ...options
    });
};

/**
 * Update Skill
 * Update skill.
 */
export const updateSkillApiV1SkillsSkillIdPut = <ThrowOnError extends boolean = false>(options: Options<UpdateSkillApiV1SkillsSkillIdPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateSkillApiV1SkillsSkillIdPutResponses, UpdateSkillApiV1SkillsSkillIdPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/{skill_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get User Skills
 * Get user's skills in current factory.
 */
export const getUserSkillsApiV1SkillsUserUserIdSkillsGet = <ThrowOnError extends boolean = false>(options: Options<GetUserSkillsApiV1SkillsUserUserIdSkillsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetUserSkillsApiV1SkillsUserUserIdSkillsGetResponses, GetUserSkillsApiV1SkillsUserUserIdSkillsGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/user/{user_id}/skills',
        ...options
    });
};

/**
 * Assign Skills To User
 * Assign skills to user in current factory.
 */
export const assignSkillsToUserApiV1SkillsAssignPost = <ThrowOnError extends boolean = false>(options: Options<AssignSkillsToUserApiV1SkillsAssignPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AssignSkillsToUserApiV1SkillsAssignPostResponses, AssignSkillsToUserApiV1SkillsAssignPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/assign',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Modify Skill Proficiency
 * Modify user's skill proficiency level.
 */
export const modifySkillProficiencyApiV1SkillsModifyProficiencyPut = <ThrowOnError extends boolean = false>(options: Options<ModifySkillProficiencyApiV1SkillsModifyProficiencyPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<ModifySkillProficiencyApiV1SkillsModifyProficiencyPutResponses, ModifySkillProficiencyApiV1SkillsModifyProficiencyPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/modify-proficiency',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Certify User Skill
 * Certify user in skill.
 */
export const certifyUserSkillApiV1SkillsCertifyPost = <ThrowOnError extends boolean = false>(options: Options<CertifyUserSkillApiV1SkillsCertifyPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CertifyUserSkillApiV1SkillsCertifyPostResponses, CertifyUserSkillApiV1SkillsCertifyPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/certify',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Remove User Skill
 * Remove skill from user.
 */
export const removeUserSkillApiV1SkillsRemoveDelete = <ThrowOnError extends boolean = false>(options: Options<RemoveUserSkillApiV1SkillsRemoveDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<RemoveUserSkillApiV1SkillsRemoveDeleteResponses, RemoveUserSkillApiV1SkillsRemoveDeleteErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/skills/remove',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get All Crafts
 * Get all crafts with optional filtering.
 */
export const getAllCraftsApiV1CraftsGet = <ThrowOnError extends boolean = false>(options?: Options<GetAllCraftsApiV1CraftsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAllCraftsApiV1CraftsGetResponses, GetAllCraftsApiV1CraftsGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/',
        ...options
    });
};

/**
 * Create Craft
 * Create a new craft (admin only).
 */
export const createCraftApiV1CraftsPost = <ThrowOnError extends boolean = false>(options: Options<CreateCraftApiV1CraftsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateCraftApiV1CraftsPostResponses, CreateCraftApiV1CraftsPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Enabled Crafts
 * Get all enabled crafts.
 */
export const getEnabledCraftsApiV1CraftsEnabledGet = <ThrowOnError extends boolean = false>(options?: Options<GetEnabledCraftsApiV1CraftsEnabledGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetEnabledCraftsApiV1CraftsEnabledGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/enabled',
        ...options
    });
};

/**
 * Delete Craft
 * Delete craft (admin only).
 */
export const deleteCraftApiV1CraftsCraftIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteCraftApiV1CraftsCraftIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteCraftApiV1CraftsCraftIdDeleteResponses, DeleteCraftApiV1CraftsCraftIdDeleteErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/{craft_id}',
        ...options
    });
};

/**
 * Get Craft By Id
 * Get craft by ID.
 */
export const getCraftByIdApiV1CraftsCraftIdGet = <ThrowOnError extends boolean = false>(options: Options<GetCraftByIdApiV1CraftsCraftIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetCraftByIdApiV1CraftsCraftIdGetResponses, GetCraftByIdApiV1CraftsCraftIdGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/{craft_id}',
        ...options
    });
};

/**
 * Update Craft
 * Update craft (admin only).
 */
export const updateCraftApiV1CraftsCraftIdPut = <ThrowOnError extends boolean = false>(options: Options<UpdateCraftApiV1CraftsCraftIdPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateCraftApiV1CraftsCraftIdPutResponses, UpdateCraftApiV1CraftsCraftIdPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/{craft_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Craft By Code
 * Get craft by code with its routes.
 */
export const getCraftByCodeApiV1CraftsCodeCraftCodeGet = <ThrowOnError extends boolean = false>(options: Options<GetCraftByCodeApiV1CraftsCodeCraftCodeGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetCraftByCodeApiV1CraftsCodeCraftCodeGetResponses, GetCraftByCodeApiV1CraftsCodeCraftCodeGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/code/{craft_code}',
        ...options
    });
};

/**
 * Create Craft Route
 * Create a new craft route.
 */
export const createCraftRouteApiV1CraftsRoutesPost = <ThrowOnError extends boolean = false>(options: Options<CreateCraftRouteApiV1CraftsRoutesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateCraftRouteApiV1CraftsRoutesPostResponses, CreateCraftRouteApiV1CraftsRoutesPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/routes',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Bulk Craft Routes
 * Create multiple craft routes at once.
 */
export const createBulkCraftRoutesApiV1CraftsRoutesBulkPost = <ThrowOnError extends boolean = false>(options: Options<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostResponses, CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/routes/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Craft Routes
 * Get all routes for a specific craft.
 */
export const getCraftRoutesApiV1CraftsRoutesCraftCraftCodeGet = <ThrowOnError extends boolean = false>(options: Options<GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetResponses, GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/routes/craft/{craft_code}',
        ...options
    });
};

/**
 * Get Skill Routes
 * Get all routes that use a specific skill.
 */
export const getSkillRoutesApiV1CraftsRoutesSkillSkillCodeGet = <ThrowOnError extends boolean = false>(options: Options<GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetResponses, GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/routes/skill/{skill_code}',
        ...options
    });
};

/**
 * Delete Craft Route
 * Delete craft route.
 */
export const deleteCraftRouteApiV1CraftsRoutesRouteIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteResponses, DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/routes/{route_id}',
        ...options
    });
};

/**
 * Update Craft Route
 * Update craft route.
 */
export const updateCraftRouteApiV1CraftsRoutesRouteIdPut = <ThrowOnError extends boolean = false>(options: Options<UpdateCraftRouteApiV1CraftsRoutesRouteIdPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateCraftRouteApiV1CraftsRoutesRouteIdPutResponses, UpdateCraftRouteApiV1CraftsRoutesRouteIdPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/routes/{route_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Reorder Craft Routes
 * Reorder routes for a craft.
 */
export const reorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePost = <ThrowOnError extends boolean = false>(options: Options<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostResponses, ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/routes/reorder/{craft_code}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get All Orders
 * Get all orders with optional filtering.
 */
export const getAllOrdersApiV1OrdersGet = <ThrowOnError extends boolean = false>(options?: Options<GetAllOrdersApiV1OrdersGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAllOrdersApiV1OrdersGetResponses, GetAllOrdersApiV1OrdersGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/',
        ...options
    });
};

/**
 * Create Order
 * Create a new order with order lines.
 */
export const createOrderApiV1OrdersPost = <ThrowOnError extends boolean = false>(options: Options<CreateOrderApiV1OrdersPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateOrderApiV1OrdersPostResponses, CreateOrderApiV1OrdersPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Order By Order No Main
 * Get order by order number with order lines, crafts, and craft routes.
 */
export const getOrderByOrderNoMainApiV1OrdersOrderNoGet = <ThrowOnError extends boolean = false>(options: Options<GetOrderByOrderNoMainApiV1OrdersOrderNoGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrderByOrderNoMainApiV1OrdersOrderNoGetResponses, GetOrderByOrderNoMainApiV1OrdersOrderNoGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_no}',
        ...options
    });
};

/**
 * Delete Order
 * Delete order (admin only).
 */
export const deleteOrderApiV1OrdersOrderIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteOrderApiV1OrdersOrderIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteOrderApiV1OrdersOrderIdDeleteResponses, DeleteOrderApiV1OrdersOrderIdDeleteErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_id}',
        ...options
    });
};

/**
 * Update Order
 * Update order (admin or owner only).
 */
export const updateOrderApiV1OrdersOrderIdPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrderApiV1OrdersOrderIdPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrderApiV1OrdersOrderIdPutResponses, UpdateOrderApiV1OrdersOrderIdPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Order Status
 * Update order status.
 */
export const updateOrderStatusApiV1OrdersOrderIdStatusPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrderStatusApiV1OrdersOrderIdStatusPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrderStatusApiV1OrdersOrderIdStatusPutResponses, UpdateOrderStatusApiV1OrdersOrderIdStatusPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_id}/status',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Start Order
 * Start an order - change status from PENDING to IN_PROGRESS.
 */
export const startOrderApiV1OrdersOrderNoStartPost = <ThrowOnError extends boolean = false>(options: Options<StartOrderApiV1OrdersOrderNoStartPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<StartOrderApiV1OrdersOrderNoStartPostResponses, StartOrderApiV1OrdersOrderNoStartPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_no}/start',
        ...options
    });
};

/**
 * Update Craft Progress
 * Update order craft progress.
 */
export const updateCraftProgressApiV1OrdersOrderIdCraftProgressPut = <ThrowOnError extends boolean = false>(options: Options<UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutResponses, UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_id}/craft-progress',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Order Amount
 * Update order total amount.
 */
export const updateOrderAmountApiV1OrdersOrderIdAmountPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrderAmountApiV1OrdersOrderIdAmountPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrderAmountApiV1OrdersOrderIdAmountPutResponses, UpdateOrderAmountApiV1OrdersOrderIdAmountPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_id}/amount',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Add Order Lines
 * Add multiple order lines to an existing order.
 */
export const addOrderLinesApiV1OrdersOrderLinesBulkPost = <ThrowOnError extends boolean = false>(options: Options<AddOrderLinesApiV1OrdersOrderLinesBulkPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AddOrderLinesApiV1OrdersOrderLinesBulkPostResponses, AddOrderLinesApiV1OrdersOrderLinesBulkPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/order-lines/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Production
 * Update production quantities for order lines.
 */
export const updateProductionApiV1OrdersProductionPut = <ThrowOnError extends boolean = false>(options: Options<UpdateProductionApiV1OrdersProductionPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateProductionApiV1OrdersProductionPutResponses, UpdateProductionApiV1OrdersProductionPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/production',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Order Statistics
 * Get order statistics summary.
 */
export const getOrderStatisticsApiV1OrdersStatisticsSummaryGet = <ThrowOnError extends boolean = false>(options?: Options<GetOrderStatisticsApiV1OrdersStatisticsSummaryGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetOrderStatisticsApiV1OrdersStatisticsSummaryGetResponses, GetOrderStatisticsApiV1OrdersStatisticsSummaryGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/statistics/summary',
        ...options
    });
};

/**
 * Get Dashboard Data
 * Get comprehensive dashboard data.
 */
export const getDashboardDataApiV1OrdersDashboardDataGet = <ThrowOnError extends boolean = false>(options?: Options<GetDashboardDataApiV1OrdersDashboardDataGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetDashboardDataApiV1OrdersDashboardDataGetResponses, GetDashboardDataApiV1OrdersDashboardDataGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/dashboard/data',
        ...options
    });
};

/**
 * Search Order Parts
 * Search order parts with optional filtering.
 */
export const searchOrderPartsApiV1OrderPartsGet = <ThrowOnError extends boolean = false>(options?: Options<SearchOrderPartsApiV1OrderPartsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<SearchOrderPartsApiV1OrderPartsGetResponses, SearchOrderPartsApiV1OrderPartsGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-parts/',
        ...options
    });
};

/**
 * Create Order Part
 * Create a new order part.
 */
export const createOrderPartApiV1OrderPartsPost = <ThrowOnError extends boolean = false>(options: Options<CreateOrderPartApiV1OrderPartsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateOrderPartApiV1OrderPartsPostResponses, CreateOrderPartApiV1OrderPartsPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-parts/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Order Parts By Order
 * Get all order parts for a specific order.
 */
export const getOrderPartsByOrderApiV1OrderPartsOrderOrderNoGet = <ThrowOnError extends boolean = false>(options: Options<GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetResponses, GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-parts/order/{order_no}',
        ...options
    });
};

/**
 * Delete Order Part
 * Delete order part and all its bundles.
 */
export const deleteOrderPartApiV1OrderPartsOrderPartIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteResponses, DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-parts/{order_part_id}',
        ...options
    });
};

/**
 * Get Order Part By Id
 * Get order part by ID.
 */
export const getOrderPartByIdApiV1OrderPartsOrderPartIdGet = <ThrowOnError extends boolean = false>(options: Options<GetOrderPartByIdApiV1OrderPartsOrderPartIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrderPartByIdApiV1OrderPartsOrderPartIdGetResponses, GetOrderPartByIdApiV1OrderPartsOrderPartIdGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-parts/{order_part_id}',
        ...options
    });
};

/**
 * Update Order Part
 * Update order part.
 */
export const updateOrderPartApiV1OrderPartsOrderPartIdPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrderPartApiV1OrderPartsOrderPartIdPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrderPartApiV1OrderPartsOrderPartIdPutResponses, UpdateOrderPartApiV1OrderPartsOrderPartIdPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-parts/{order_part_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Order Part With Bundles
 * Get order part by ID with order bundles.
 */
export const getOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGet = <ThrowOnError extends boolean = false>(options: Options<GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetResponses, GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-parts/{order_part_id}/with-bundles',
        ...options
    });
};

/**
 * Update Order Part Status
 * Update order part status.
 */
export const updateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutResponses, UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-parts/{order_part_id}/status',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Bulk Create Order Parts
 * Create multiple order parts at once.
 */
export const bulkCreateOrderPartsApiV1OrderPartsBulkPost = <ThrowOnError extends boolean = false>(options: Options<BulkCreateOrderPartsApiV1OrderPartsBulkPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<BulkCreateOrderPartsApiV1OrderPartsBulkPostResponses, BulkCreateOrderPartsApiV1OrderPartsBulkPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-parts/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Order Part Statistics
 * Get order part statistics summary.
 */
export const getOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGet = <ThrowOnError extends boolean = false>(options?: Options<GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetResponses, GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-parts/statistics/summary',
        ...options
    });
};

/**
 * Search Order Bundles
 * Search order bundles with optional filtering.
 */
export const searchOrderBundlesApiV1OrderBundlesGet = <ThrowOnError extends boolean = false>(options?: Options<SearchOrderBundlesApiV1OrderBundlesGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<SearchOrderBundlesApiV1OrderBundlesGetResponses, SearchOrderBundlesApiV1OrderBundlesGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/',
        ...options
    });
};

/**
 * Create Order Bundle
 * Create a new order bundle.
 */
export const createOrderBundleApiV1OrderBundlesPost = <ThrowOnError extends boolean = false>(options: Options<CreateOrderBundleApiV1OrderBundlesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateOrderBundleApiV1OrderBundlesPostResponses, CreateOrderBundleApiV1OrderBundlesPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Order Bundles By Order Part
 * Get all order bundles for a specific order part.
 */
export const getOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGet = <ThrowOnError extends boolean = false>(options: Options<GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetResponses, GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/order-part/{order_part_no}/order/{order_no}',
        ...options
    });
};

/**
 * Get Order Bundles By Order
 * Get all order bundles for a specific order.
 */
export const getOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGet = <ThrowOnError extends boolean = false>(options: Options<GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetResponses, GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/order/{order_no}',
        ...options
    });
};

/**
 * Delete Order Bundle
 * Delete order bundle.
 */
export const deleteOrderBundleApiV1OrderBundlesOrderBundleIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteResponses, DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/{order_bundle_id}',
        ...options
    });
};

/**
 * Get Order Bundle By Id
 * Get order bundle by ID.
 */
export const getOrderBundleByIdApiV1OrderBundlesOrderBundleIdGet = <ThrowOnError extends boolean = false>(options: Options<GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetResponses, GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/{order_bundle_id}',
        ...options
    });
};

/**
 * Update Order Bundle
 * Update order bundle.
 */
export const updateOrderBundleApiV1OrderBundlesOrderBundleIdPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutResponses, UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/{order_bundle_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Order Bundle Status
 * Update order bundle status.
 */
export const updateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutResponses, UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/{order_bundle_id}/status',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Order Bundle Production
 * Update order bundle production progress.
 */
export const updateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutResponses, UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/{order_bundle_id}/production',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Bulk Create Order Bundles
 * Create multiple order bundles at once.
 */
export const bulkCreateOrderBundlesApiV1OrderBundlesBulkPost = <ThrowOnError extends boolean = false>(options: Options<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<BulkCreateOrderBundlesApiV1OrderBundlesBulkPostResponses, BulkCreateOrderBundlesApiV1OrderBundlesBulkPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Order Bundle Statistics
 * Get order bundle statistics summary.
 */
export const getOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGet = <ThrowOnError extends boolean = false>(options?: Options<GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetResponses, GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/order-bundles/statistics/summary',
        ...options
    });
};

/**
 * Get Order Crafts By Order
 * Get all order crafts for an order.
 */
export const getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGet = <ThrowOnError extends boolean = false>(options: Options<GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetResponses, GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_no}/crafts',
        ...options
    });
};

/**
 * Create Order Crafts For Order
 * Create order crafts configuration for an order.
 */
export const createOrderCraftsForOrderApiV1OrdersOrderNoCraftsPost = <ThrowOnError extends boolean = false>(options: Options<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostResponses, CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_no}/crafts',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Order Craft By Id
 * Get order craft by ID.
 */
export const getOrderCraftByIdApiV1CraftsOrderCraftIdGet = <ThrowOnError extends boolean = false>(options: Options<GetOrderCraftByIdApiV1CraftsOrderCraftIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrderCraftByIdApiV1CraftsOrderCraftIdGetResponses, GetOrderCraftByIdApiV1CraftsOrderCraftIdGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/{order_craft_id}',
        ...options
    });
};

/**
 * Update Order Craft Status
 * Update order craft status.
 */
export const updateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutResponses, UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/crafts/{order_craft_id}/status',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Order Craft Route Status
 * Update order craft route status.
 */
export const updateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutResponses, UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/craft-routes/{order_craft_route_id}/status',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Next Craft For Order
 * Get the next pending craft for an order.
 */
export const getNextCraftForOrderApiV1OrdersOrderNoCraftsNextGet = <ThrowOnError extends boolean = false>(options: Options<GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetResponses, GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_no}/crafts/next',
        ...options
    });
};

/**
 * Get Current Craft For Order
 * Get the currently in-progress craft for an order.
 */
export const getCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGet = <ThrowOnError extends boolean = false>(options: Options<GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetResponses, GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/orders/{order_no}/crafts/current',
        ...options
    });
};

/**
 * Get Order Craft Statistics
 * Get order craft statistics.
 */
export const getOrderCraftStatisticsApiV1StatisticsGet = <ThrowOnError extends boolean = false>(options?: Options<GetOrderCraftStatisticsApiV1StatisticsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetOrderCraftStatisticsApiV1StatisticsGetResponses, GetOrderCraftStatisticsApiV1StatisticsGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/statistics',
        ...options
    });
};

/**
 * Create Craft Instance
 * Create a new craft instance - worker completion record.
 */
export const createCraftInstanceApiV1CraftInstancesPost = <ThrowOnError extends boolean = false>(options: Options<CreateCraftInstanceApiV1CraftInstancesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateCraftInstanceApiV1CraftInstancesPostResponses, CreateCraftInstanceApiV1CraftInstancesPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/craft-instances/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Qr Scan Register
 * Register completion via QR code scanning.
 */
export const qrScanRegisterApiV1CraftInstancesQrScanPost = <ThrowOnError extends boolean = false>(options: Options<QrScanRegisterApiV1CraftInstancesQrScanPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<QrScanRegisterApiV1CraftInstancesQrScanPostResponses, QrScanRegisterApiV1CraftInstancesQrScanPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/craft-instances/qr-scan',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Search Craft Instances
 * Search craft instances with filters.
 */
export const searchCraftInstancesApiV1CraftInstancesSearchGet = <ThrowOnError extends boolean = false>(options?: Options<SearchCraftInstancesApiV1CraftInstancesSearchGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<SearchCraftInstancesApiV1CraftInstancesSearchGetResponses, SearchCraftInstancesApiV1CraftInstancesSearchGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/craft-instances/search',
        ...options
    });
};

/**
 * Get Craft Instance
 * Get craft instance by ID.
 */
export const getCraftInstanceApiV1CraftInstancesInstanceIdGet = <ThrowOnError extends boolean = false>(options: Options<GetCraftInstanceApiV1CraftInstancesInstanceIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetCraftInstanceApiV1CraftInstancesInstanceIdGetResponses, GetCraftInstanceApiV1CraftInstancesInstanceIdGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/craft-instances/{instance_id}',
        ...options
    });
};

/**
 * Verify Craft Instance
 * Verify a craft instance.
 */
export const verifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPost = <ThrowOnError extends boolean = false>(options: Options<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostResponses, VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/craft-instances/{instance_id}/verify',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Reject Craft Instance
 * Reject a craft instance.
 */
export const rejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPost = <ThrowOnError extends boolean = false>(options: Options<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostResponses, RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/craft-instances/{instance_id}/reject',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Craft Instance Statistics
 * Get craft instance statistics.
 */
export const getCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGet = <ThrowOnError extends boolean = false>(options?: Options<GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetResponses, GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/craft-instances/statistics/overview',
        ...options
    });
};

/**
 * Get Available Registration Data
 * Get available registration data for an order - returns parts/bundles that can still be registered.
 */
export const getAvailableRegistrationData = <ThrowOnError extends boolean = false>(options: Options<GetAvailableRegistrationDataData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetAvailableRegistrationDataResponses, GetAvailableRegistrationDataErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/available-registration/{order_no}',
        ...options
    });
};

/**
 * Get Registration Summary
 * Get registration summary for an order - overview of completion status.
 */
export const getRegistrationSummary = <ThrowOnError extends boolean = false>(options: Options<GetRegistrationSummaryData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetRegistrationSummaryResponses, GetRegistrationSummaryErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/v1/available-registration/{order_no}/summary',
        ...options
    });
};

/**
 * Root
 * Root endpoint.
 */
export const rootGet = <ThrowOnError extends boolean = false>(options?: Options<RootGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<RootGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        url: '/',
        ...options
    });
};

/**
 * Health Check
 * Health check endpoint.
 */
export const healthCheckHealthGet = <ThrowOnError extends boolean = false>(options?: Options<HealthCheckHealthGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<HealthCheckHealthGetResponses, unknown, ThrowOnError>({
        responseType: 'json',
        url: '/health',
        ...options
    });
};