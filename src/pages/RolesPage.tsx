import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Plus, Pencil, Trash2, Eye, Shield } from 'lucide-react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { PermissionTree } from "@/components/PermissionTree"
import AppLayout from "@/components/AppLayout"
import { 
  getAllRolesApiV1RolesGetOptions,
  createRoleApiV1RolesPostMutation,
  updateRoleApiV1RolesRoleIdPutMutation,
  deleteRoleApiV1RolesRoleIdDeleteMutation,
  getRoleByIdApiV1RolesRoleIdGetOptions,
  getAllPermissionsApiV1PermissionsListGetOptions
} from '@/services/@tanstack/react-query.gen'
import type { RoleResponseDto, RoleCreateDto, PermissionResponseDto } from '@/services/types.gen'

// Modal for creating/editing roles
interface RoleModalProps {
  isOpen: boolean
  onClose: () => void
  role?: RoleResponseDto | null
  permissions: PermissionResponseDto[]
}

function RoleModal({ isOpen, onClose, role, permissions }: RoleModalProps) {
  const [formData, setFormData] = useState({
    name: role?.name || '',
    description: role?.description || '',
    is_active: role?.is_active ?? true,
    permission_ids: role?.permissions?.map(p => p.id) || []
  })

  const queryClient = useQueryClient()
  
  const createMutation = useMutation({
    ...createRoleApiV1RolesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getAllRolesApiV1RolesGet'] })
      onClose()
      setFormData({ name: '', description: '', is_active: true, permission_ids: [] })
    }
  })

  const updateMutation = useMutation({
    ...updateRoleApiV1RolesRoleIdPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getAllRolesApiV1RolesGet'] })
      onClose()
    }
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (role) {
      // Update existing role
      updateMutation.mutate({
        path: { role_id: role.id },
        body: formData as RoleCreateDto
      })
    } else {
      // Create new role
      createMutation.mutate({
        body: formData as RoleCreateDto
      })
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-background p-6 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-semibold mb-4">
          {role ? '编辑角色' : '创建角色'}
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">角色名称 *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="description">描述</Label>
            <Input
              id="description"
              value={formData.description || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
              className="rounded"
            />
            <Label htmlFor="is_active">激活状态</Label>
          </div>
          
          <div>
            <Label>权限分配</Label>
            <PermissionTree
              permissions={permissions}
              selectedIds={formData.permission_ids}
              onPermissionChange={(permissionIds) => 
                setFormData(prev => ({ ...prev, permission_ids: permissionIds }))
              }
            />
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button 
              type="submit" 
              disabled={createMutation.isPending || updateMutation.isPending}
            >
              {createMutation.isPending || updateMutation.isPending ? '保存中...' : '保存'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}

// Role details modal
interface RoleDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  roleId: number | null
}

function RoleDetailsModal({ isOpen, onClose, roleId }: RoleDetailsModalProps) {
  const { data: role, isLoading } = useQuery({
    ...getRoleByIdApiV1RolesRoleIdGetOptions({ path: { role_id: roleId! } }),
    enabled: isOpen && roleId !== null
  })

  // Fetch all permissions to build the tree structure
  const { data: allPermissionsData } = useQuery({
    ...getAllPermissionsApiV1PermissionsListGetOptions(),
    enabled: isOpen && roleId !== null
  })

  if (!isOpen || !roleId) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-background p-6 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-semibold mb-4">角色详情</h2>
        
        {isLoading ? (
          <div>加载中...</div>
        ) : role ? (
          <div className="space-y-4">
            <div>
              <Label>角色名称</Label>
              <p className="text-sm text-muted-foreground">{role.name}</p>
            </div>
            
            <div>
              <Label>描述</Label>
              <p className="text-sm text-muted-foreground">{role.description || '无描述'}</p>
            </div>
            
            <div>
              <Label>状态</Label>
              <p className="text-sm text-muted-foreground">
                {role.is_active ? '激活' : '禁用'}
              </p>
            </div>
            
            <div>
              <Label>权限列表</Label>
              <div className="mt-2">
                {role.permissions && role.permissions.length > 0 && allPermissionsData?.permissions ? (
                  <PermissionTree
                    permissions={allPermissionsData.permissions}
                    selectedIds={role.permissions.map(p => p.id)}
                    onPermissionChange={() => {}} // Read-only view
                    readOnly={true}
                  />
                ) : (
                  <p className="text-sm text-muted-foreground">无权限</p>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div>角色未找到</div>
        )}
        
        <div className="flex justify-end mt-4">
          <Button onClick={onClose}>关闭</Button>
        </div>
      </div>
    </div>
  )
}

export default function RolesPage() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingRole, setEditingRole] = useState<RoleResponseDto | null>(null)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [selectedRoleId, setSelectedRoleId] = useState<number | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  const queryClient = useQueryClient()

  // Fetch all roles
  const { data: rolesData, isLoading: rolesLoading } = useQuery({
    ...getAllRolesApiV1RolesGetOptions()
  })

  // Fetch all permissions for role creation/editing
  const { data: permissionsData } = useQuery({
    ...getAllPermissionsApiV1PermissionsListGetOptions()
  })

  const deleteMutation = useMutation({
    ...deleteRoleApiV1RolesRoleIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getAllRolesApiV1RolesGet'] })
    }
  })

  const handleEdit = (role: RoleResponseDto) => {
    setEditingRole(role)
    setIsModalOpen(true)
  }

  const handleDelete = (roleId: number) => {
    if (confirm('确定要删除这个角色吗？')) {
      deleteMutation.mutate({
        path: { role_id: roleId }
      })
    }
  }

  const handleView = (roleId: number) => {
    setSelectedRoleId(roleId)
    setIsDetailsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingRole(null)
  }

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false)
    setSelectedRoleId(null)
  }

  const filteredRoles = rolesData?.roles?.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (role.description && role.description.toLowerCase().includes(searchTerm.toLowerCase()))
  ) || []

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">角色管理</h1>
            <p className="text-muted-foreground">
              管理系统角色和权限分配
            </p>
          </div>
          <Button onClick={() => setIsModalOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            新建角色
          </Button>
        </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            角色列表
          </CardTitle>
          <CardDescription>
            总共 {rolesData?.total || 0} 个角色
          </CardDescription>
          <div className="flex items-center space-x-2">
            <Input
              placeholder="搜索角色名称或描述..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
        </CardHeader>
        <CardContent>
          {rolesLoading ? (
            <div className="flex items-center justify-center h-32">
              <div>加载中...</div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>角色名称</TableHead>
                  <TableHead>描述</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>权限数量</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRoles.map((role) => (
                  <TableRow key={role.id}>
                    <TableCell className="font-medium">{role.name}</TableCell>
                    <TableCell>{role.description || '无描述'}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        role.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {role.is_active ? '激活' : '禁用'}
                      </span>
                    </TableCell>
                    <TableCell>{role.permissions?.length || 0}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleView(role.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(role)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(role.id)}
                          disabled={deleteMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredRoles.length === 0 && !rolesLoading && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      {searchTerm ? '没有找到匹配的角色' : '暂无角色数据'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Role Create/Edit Modal */}
      <RoleModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        role={editingRole}
        permissions={permissionsData?.permissions || []}
      />

      {/* Role Details Modal */}
      <RoleDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        roleId={selectedRoleId}
      />
      </div>
    </AppLayout>
  )
}
