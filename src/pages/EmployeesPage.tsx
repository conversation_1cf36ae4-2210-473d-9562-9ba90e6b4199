import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import AppLayout from "@/components/AppLayout"
import { Plus, Trash2, User, Settings, Search, UserMinus } from "lucide-react"
import { 
  getFactoryUserListApiV1UserManagementUserListGetOptions,
  getAvailableUsersApiV1UserManagementAvailableUsersGetOptions,
  addUsersToFactoryApiV1UserManagementAddUsersPostMutation,
  bindUserRoleApiV1UserManagementBindRolesPostMutation,
  suspendUserInFactoryApiV1UserManagementSuspendPostMutation,
  removeUserFromFactoryApiV1UserManagementRemoveUserDeleteMutation,
  getAllRolesApiV1RolesGetOptions
} from '@/services/@tanstack/react-query.gen'
import type { UserSummaryDto } from '@/services/types.gen'

export default function EmployeesPage() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<UserSummaryDto | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRoleFilter, setSelectedRoleFilter] = useState<string>('all')
  const [selectedUsersToAdd, setSelectedUsersToAdd] = useState<number[]>([])
  const [selectedRoleId, setSelectedRoleId] = useState<number | null>(null)

  const queryClient = useQueryClient()

  // Fetch factory users
  const { data: usersData, isLoading: isUsersLoading } = useQuery({
    ...getFactoryUserListApiV1UserManagementUserListGetOptions(),
    staleTime: 30000,
  })

  // Fetch available users to add
  const { data: availableUsersData, isLoading: isAvailableUsersLoading } = useQuery({
    ...getAvailableUsersApiV1UserManagementAvailableUsersGetOptions({
      query: { 
        search_term: searchTerm || undefined,
        role_id: selectedRoleFilter !== 'all' ? parseInt(selectedRoleFilter) : undefined,
        is_active: true
      }
    }),
    enabled: isAddModalOpen,
    staleTime: 30000,
  })

  // Fetch roles for filtering and assignment
  const { data: rolesData } = useQuery({
    ...getAllRolesApiV1RolesGetOptions(),
    staleTime: 300000, // 5 minutes
  })

  // Mutations
  const addUsersMutation = useMutation({
    ...addUsersToFactoryApiV1UserManagementAddUsersPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getFactoryUserListApiV1UserManagementUserListGet'] })
      alert('用户添加成功')
      setIsAddModalOpen(false)
      setSelectedUsersToAdd([])
    },
    onError: (error) => {
      alert(`添加用户失败: ${error.message}`)
    }
  })

  const bindRoleMutation = useMutation({
    ...bindUserRoleApiV1UserManagementBindRolesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getFactoryUserListApiV1UserManagementUserListGet'] })
      alert('角色绑定成功')
      setIsRoleModalOpen(false)
      setSelectedUser(null)
      setSelectedRoleId(null)
    },
    onError: (error) => {
      alert(`角色绑定失败: ${error.message}`)
    }
  })

  const suspendUserMutation = useMutation({
    ...suspendUserInFactoryApiV1UserManagementSuspendPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getFactoryUserListApiV1UserManagementUserListGet'] })
      alert('用户已暂停')
    },
    onError: (error) => {
      alert(`暂停用户失败: ${error.message}`)
    }
  })

  const removeUserMutation = useMutation({
    ...removeUserFromFactoryApiV1UserManagementRemoveUserDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getFactoryUserListApiV1UserManagementUserListGet'] })
      alert('用户已移除')
    },
    onError: (error) => {
      alert(`移除用户失败: ${error.message}`)
    }
  })

  const handleAddUsers = () => {
    if (selectedUsersToAdd.length === 0) {
      alert('请选择要添加的用户')
      return
    }

    addUsersMutation.mutate({
      body: {
        users: selectedUsersToAdd.map(user_id => ({ user_id }))
      }
    })
  }

  const handleBindRole = () => {
    if (!selectedUser || !selectedRoleId) {
      alert('请选择用户和角色')
      return
    }

    bindRoleMutation.mutate({
      body: {
        user_id: selectedUser.id,
        role_id: selectedRoleId
      }
    })
  }

  const handleSuspendUser = (userId: number) => {
    if (confirm('确定要暂停此用户吗？')) {
      suspendUserMutation.mutate({
        body: { user_id: userId }
      })
    }
  }

  const handleRemoveUser = (userId: number) => {
    if (confirm('确定要从工厂中移除此用户吗？')) {
      removeUserMutation.mutate({
        body: { user_id: userId }
      })
    }
  }

  const openRoleModal = (user: UserSummaryDto) => {
    setSelectedUser(user)
    setIsRoleModalOpen(true)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'bg-green-100 text-green-800'
      case 'SUSPENDED': return 'bg-red-100 text-red-800'
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'APPROVED': return '已批准'
      case 'SUSPENDED': return '已暂停'
      case 'PENDING': return '待审批'
      default: return status
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-purple-100 text-purple-800'
      case 'MANAGER': return 'bg-blue-100 text-blue-800'
      case 'SUPERVISOR': return 'bg-green-100 text-green-800'
      case 'WORKER': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleText = (role: string) => {
    switch (role) {
      case 'ADMIN': return '管理员'
      case 'MANAGER': return '经理'
      case 'SUPERVISOR': return '主管'
      case 'WORKER': return '工人'
      default: return role
    }
  }

  return (
    <AppLayout>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">用户管理</h1>
          <p className="text-muted-foreground">管理工厂内的用户信息和权限</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            添加用户
          </Button>
        </div>
      </div>

      {/* Filter Section */}
      <div className="mb-6 flex items-center space-x-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="搜索用户..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Select value={selectedRoleFilter} onValueChange={setSelectedRoleFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="筛选角色" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有角色</SelectItem>
            {rolesData?.roles?.map((role) => (
              <SelectItem key={role.id} value={role.id.toString()}>
                {role.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Users List */}
      {isUsersLoading ? (
        <div className="text-center py-8">加载中...</div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {usersData?.users?.map((factoryUser) => (
            <Card key={factoryUser.user.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-full">
                      <User className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{factoryUser.user.full_name || factoryUser.user.username}</CardTitle>
                      <CardDescription>{factoryUser.user.username}</CardDescription>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => openRoleModal(factoryUser.user)}
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleSuspendUser(factoryUser.user.id)}
                    >
                      <UserMinus className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleRemoveUser(factoryUser.user.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">邮箱：</span>
                    <span className="text-sm font-medium">{factoryUser.user.email}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">工厂状态：</span>
                    <Badge className={getStatusColor(factoryUser.factory_status)}>
                      {getStatusText(factoryUser.factory_status)}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">工厂角色：</span>
                    <Badge className={getRoleColor(factoryUser.factory_role)}>
                      {getRoleText(factoryUser.factory_role)}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">账户状态：</span>
                    <Badge variant={factoryUser.user.is_active ? "default" : "secondary"}>
                      {factoryUser.user.is_active ? '激活' : '禁用'}
                    </Badge>
                  </div>
                  {factoryUser.skills_count !== undefined && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">技能数量：</span>
                      <span className="text-sm font-medium">
                        {factoryUser.certified_skills_count || 0} / {factoryUser.skills_count}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add Users Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>添加用户到工厂</DialogTitle>
            <DialogDescription>
              选择要添加到当前工厂的用户
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <Input
                  placeholder="搜索用户..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={selectedRoleFilter} onValueChange={setSelectedRoleFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="筛选角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有角色</SelectItem>
                  {rolesData?.roles?.map((role) => (
                    <SelectItem key={role.id} value={role.id.toString()}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {isAvailableUsersLoading ? (
                <div className="text-center py-8">加载中...</div>
              ) : (
                <div className="space-y-2">
                  {availableUsersData?.users?.map((user) => (
                    <div
                      key={user.id}
                      className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedUsersToAdd.includes(user.id)
                          ? 'bg-primary/10 border-primary'
                          : 'hover:bg-gray-50'
                      }`}
                      onClick={() => {
                        setSelectedUsersToAdd(prev => 
                          prev.includes(user.id)
                            ? prev.filter(id => id !== user.id)
                            : [...prev, user.id]
                        )
                      }}
                    >
                      <input
                        type="checkbox"
                        checked={selectedUsersToAdd.includes(user.id)}
                        onChange={() => {}}
                        className="rounded"
                      />
                      <div className="flex items-center space-x-3 flex-1">
                        <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                          <User className="w-4 h-4 text-primary" />
                        </div>
                        <div>
                          <p className="font-medium">{user.full_name || user.username}</p>
                          <p className="text-sm text-muted-foreground">{user.email}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
              取消
            </Button>
            <Button onClick={handleAddUsers} disabled={selectedUsersToAdd.length === 0}>
              添加用户 ({selectedUsersToAdd.length})
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Role Binding Modal */}
      <Dialog open={isRoleModalOpen} onOpenChange={setIsRoleModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>分配角色</DialogTitle>
            <DialogDescription>
              为用户 {selectedUser?.full_name || selectedUser?.username} 分配角色
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="role-select">选择角色</Label>
              <Select value={selectedRoleId?.toString()} onValueChange={(value: string) => setSelectedRoleId(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择角色" />
                </SelectTrigger>
                <SelectContent>
                  {rolesData?.roles?.map((role) => (
                    <SelectItem key={role.id} value={role.id.toString()}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRoleModalOpen(false)}>
              取消
            </Button>
            <Button onClick={handleBindRole} disabled={!selectedRoleId}>
              分配角色
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  )
}
