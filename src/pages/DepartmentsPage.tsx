import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import AppLayout from "@/components/AppLayout"
import { Plus, Edit2, Trash2 } from "lucide-react"

interface Department {
  id: number
  name: string
  description: string
  manager: string
  employeeCount: number
}

export default function DepartmentsPage() {
  const [departments, setDepartments] = useState<Department[]>([
    { id: 1, name: '裁剪部', description: '负责服装裁剪工艺', manager: '张三', employeeCount: 25 },
    { id: 2, name: '车缝部', description: '负责服装车缝制作', manager: '李四', employeeCount: 32 },
    { id: 3, name: '整烫部', description: '负责服装整烫处理', manager: '王五', employeeCount: 15 },
    { id: 4, name: '包装部', description: '负责产品包装整理', manager: '赵六', employeeCount: 18 },
  ])

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [newDepartment, setNewDepartment] = useState({
    name: '',
    description: '',
    manager: ''
  })

  const handleAddDepartment = () => {
    if (newDepartment.name && newDepartment.description && newDepartment.manager) {
      const department: Department = {
        id: departments.length + 1,
        ...newDepartment,
        employeeCount: 0
      }
      setDepartments([...departments, department])
      setNewDepartment({ name: '', description: '', manager: '' })
      setIsModalOpen(false)
    }
  }

  const handleDeleteDepartment = (id: number) => {
    setDepartments(departments.filter(dept => dept.id !== id))
  }

  return (
    <AppLayout>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">部门管理</h1>
          <p className="text-muted-foreground">管理服装生产各部门信息</p>
        </div>
        <Button onClick={() => setIsModalOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          新增部门
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {departments.map((department) => (
          <Card key={department.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{department.name}</CardTitle>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="sm">
                    <Edit2 className="w-4 h-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDeleteDepartment(department.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>{department.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">部门主管：</span>
                  <span className="text-sm font-medium">{department.manager}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">员工数量：</span>
                  <span className="text-sm font-medium">{department.employeeCount}人</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 添加部门模态框 */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle>新增部门</CardTitle>
              <CardDescription>添加新的生产部门</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">部门名称</Label>
                <Input
                  id="name"
                  value={newDepartment.name}
                  onChange={(e) => setNewDepartment({...newDepartment, name: e.target.value})}
                  placeholder="请输入部门名称"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">部门描述</Label>
                <Input
                  id="description"
                  value={newDepartment.description}
                  onChange={(e) => setNewDepartment({...newDepartment, description: e.target.value})}
                  placeholder="请输入部门描述"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="manager">部门主管</Label>
                <Input
                  id="manager"
                  value={newDepartment.manager}
                  onChange={(e) => setNewDepartment({...newDepartment, manager: e.target.value})}
                  placeholder="请输入主管姓名"
                />
              </div>
              <div className="flex space-x-2">
                <Button onClick={handleAddDepartment} className="flex-1">
                  确认添加
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setIsModalOpen(false)}
                  className="flex-1"
                >
                  取消
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </AppLayout>
  )
}
