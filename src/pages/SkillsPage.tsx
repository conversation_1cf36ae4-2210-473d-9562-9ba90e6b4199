import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Plus, Search, Filter, Edit2, Trash2, Users, Award, MoreHorizontal } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from 'sonner'
import AppLayout from '@/components/AppLayout'
import { SkillDialog } from '@/components/skills'
import { 
  getAllSkillsApiV1SkillsGetOptions,
  createSkillApiV1SkillsPostMutation,
  updateSkillApiV1SkillsSkillIdPutMutation,
  deleteSkillApiV1SkillsSkillIdDeleteMutation,
  assignSkillsToUserApiV1SkillsAssignPostMutation,
  certifyUserSkillApiV1SkillsCertifyPostMutation
} from '@/services/@tanstack/react-query.gen'
import type { SkillResponseDto, SkillCreateDto, SkillUpdateDto } from '@/services/types.gen'

// 技能分类选项
const SKILL_CATEGORIES = [
  { value: 'production', label: '生产技能' },
  { value: 'quality', label: '质量控制' },
  { value: 'maintenance', label: '设备维护' },
  { value: 'safety', label: '安全操作' },
  { value: 'management', label: '管理技能' },
  { value: 'technical', label: '技术技能' },
  { value: 'other', label: '其他' }
]

// 熟练度级别选项
const PROFICIENCY_LEVELS = [
  { value: 'beginner', label: '初级', color: 'bg-red-100 text-red-800' },
  { value: 'intermediate', label: '中级', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'advanced', label: '高级', color: 'bg-blue-100 text-blue-800' },
  { value: 'expert', label: '专家', color: 'bg-green-100 text-green-800' }
]

export default function SkillsPage() {
  const queryClient = useQueryClient()
  const [activeTab, setActiveTab] = useState('skills')
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [isSkillDialogOpen, setIsSkillDialogOpen] = useState(false)
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false)
  const [selectedSkill, setSelectedSkill] = useState<SkillResponseDto | null>(null)
  const [selectedUserSkills, setSelectedUserSkills] = useState<number[]>([])

  // 分配技能表单状态
  const [assignForm, setAssignForm] = useState<{
    user_id: number
    skill_id: number
    proficiency_level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  }>({
    user_id: 0,
    skill_id: 0,
    proficiency_level: 'beginner'
  })

  // 获取所有技能
  const { data: skillsData } = useQuery(
    getAllSkillsApiV1SkillsGetOptions()
  )

  // 获取工厂用户列表 - 备用
  // const { data: usersData } = useQuery(
  //   getFactoryUserListApiV1UserManagementUserListGetOptions()
  // )

  // 简化数据访问
  const skills = skillsData?.skills || []

  // 模拟用户技能数据 - 实际应该从API获取
  const userSkills = [
    {
      id: 1,
      user_id: 1,
      user_name: '张三',
      skill_id: 1,
      skill_name: '缝纫机操作',
      proficiency_level: 'advanced',
      is_certified: true,
      certified_at: '2024-02-01',
      assigned_at: '2024-01-20'
    }
  ]

  // 创建技能mutation
  const createSkillMutation = useMutation(createSkillApiV1SkillsPostMutation())
  
  // 更新技能mutation
  const updateSkillMutation = useMutation(updateSkillApiV1SkillsSkillIdPutMutation())
  
  // 删除技能mutation
  const deleteSkillMutation = useMutation(deleteSkillApiV1SkillsSkillIdDeleteMutation())
  
  // 分配技能mutation
  const assignSkillMutation = useMutation(assignSkillsToUserApiV1SkillsAssignPostMutation())
  
  // 认证技能mutation
  const certifySkillMutation = useMutation(certifyUserSkillApiV1SkillsCertifyPostMutation())

  // 过滤技能
  const filteredSkills = skills.filter((skill) => {
    const matchesSearch = skill.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         skill.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = !categoryFilter || categoryFilter === 'all' || skill.category === categoryFilter
    const matchesStatus = !statusFilter || statusFilter === 'all' ||
                         (statusFilter === 'active' && skill.is_active) || 
                         (statusFilter === 'inactive' && !skill.is_active)
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  // 过滤用户技能
  const filteredUserSkills = userSkills.filter((userSkill) => {
    return userSkill.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
           userSkill.skill_name.toLowerCase().includes(searchTerm.toLowerCase())
  })

  // 获取分类标签
  const getCategoryLabel = (category: string) => {
    return SKILL_CATEGORIES.find(c => c.value === category)?.label || category
  }

  // 获取熟练度标签和样式
  const getProficiencyInfo = (level: string) => {
    return PROFICIENCY_LEVELS.find(p => p.value === level) || PROFICIENCY_LEVELS[0]
  }

  // 处理技能提交（创建或更新）
  const handleSkillSubmit = async (data: SkillCreateDto | { id: number; data: SkillUpdateDto }) => {
    try {
      if ('id' in data) {
        // 编辑模式
        await updateSkillMutation.mutateAsync({
          path: { skill_id: data.id },
          body: data.data
        })
        toast.success('技能更新成功')
      } else {
        // 创建模式
        await createSkillMutation.mutateAsync({
          body: data
        })
        toast.success('技能创建成功')
      }
      setIsSkillDialogOpen(false)
      setSelectedSkill(null)
      queryClient.invalidateQueries({ queryKey: getAllSkillsApiV1SkillsGetOptions().queryKey })
    } catch {
      toast.error(selectedSkill ? '技能更新失败' : '技能创建失败')
    }
  }

  // 处理删除技能
  const handleDeleteSkill = async (skillId: number) => {
    try {
      await deleteSkillMutation.mutateAsync({
        path: { skill_id: skillId }
      })
      queryClient.invalidateQueries({ queryKey: getAllSkillsApiV1SkillsGetOptions().queryKey })
      toast.success('技能删除成功')
    } catch {
      toast.error('技能删除失败')
    }
  }

  // 处理分配技能
  const handleAssignSkill = async () => {
    try {
      await assignSkillMutation.mutateAsync({
        body: {
          user_id: assignForm.user_id,
          skills: [{
            user_id: assignForm.user_id,
            skill_id: assignForm.skill_id,
            proficiency_level: assignForm.proficiency_level
          }]
        }
      })
      setAssignForm({ user_id: 0, skill_id: 0, proficiency_level: 'beginner' })
      setIsAssignDialogOpen(false)
      queryClient.invalidateQueries({ queryKey: getAllSkillsApiV1SkillsGetOptions().queryKey })
      toast.success('技能分配成功')
    } catch {
      toast.error('技能分配失败')
    }
  }

  // 处理认证技能
  const handleCertifySkill = async (userSkillId: number) => {
    try {
      await certifySkillMutation.mutateAsync({
        body: {
          user_factory_skill_id: userSkillId
        }
      })
      queryClient.invalidateQueries({ queryKey: getAllSkillsApiV1SkillsGetOptions().queryKey })
      toast.success('技能认证成功')
    } catch {
      toast.error('技能认证失败')
    }
  }

  // 打开编辑对话框
  const openEditDialog = (skill: SkillResponseDto) => {
    setSelectedSkill(skill)
    setIsSkillDialogOpen(true)
  }

  // 打开创建对话框
  const openCreateDialog = () => {
    setSelectedSkill(null)
    setIsSkillDialogOpen(true)
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* 页面标题和操作按钮 */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">技能管理</h1>
            <p className="text-muted-foreground">管理员工技能和技能认证</p>
          </div>
          <div className="flex gap-2">
            <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  分配技能
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>分配技能给员工</DialogTitle>
                  <DialogDescription>
                    为员工分配新的技能并设置熟练度级别
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="assign-user">选择员工</Label>
                    <Select value={assignForm.user_id.toString()} onValueChange={(value) => setAssignForm({...assignForm, user_id: parseInt(value)})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择员工" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">张三</SelectItem>
                        <SelectItem value="2">李四</SelectItem>
                        <SelectItem value="3">王五</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="assign-skill">选择技能</Label>
                    <Select value={assignForm.skill_id.toString()} onValueChange={(value) => setAssignForm({...assignForm, skill_id: parseInt(value)})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择技能" />
                      </SelectTrigger>
                      <SelectContent>
                        {skills.filter(s => s.is_active).map(skill => (
                          <SelectItem key={skill.id} value={skill.id.toString()}>
                            {skill.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="assign-proficiency">熟练度级别</Label>
                    <Select value={assignForm.proficiency_level} onValueChange={(value: 'beginner' | 'intermediate' | 'advanced' | 'expert') => setAssignForm({...assignForm, proficiency_level: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择熟练度" />
                      </SelectTrigger>
                      <SelectContent>
                        {PROFICIENCY_LEVELS.map(level => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
                    取消
                  </Button>
                  <Button onClick={handleAssignSkill} disabled={assignSkillMutation.isPending || !assignForm.user_id || !assignForm.skill_id}>
                    分配技能
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <Button onClick={openCreateDialog}>
              <Plus className="h-4 w-4 mr-2" />
              新建技能
            </Button>
          </div>
        </div>

        {/* 搜索和过滤栏 */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索技能或员工..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-40">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="技能分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有分类</SelectItem>
                {SKILL_CATEGORIES.map(category => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="active">启用</SelectItem>
                <SelectItem value="inactive">禁用</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 主要内容 - 标签页 */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="skills">技能管理</TabsTrigger>
            <TabsTrigger value="user-skills">员工技能</TabsTrigger>
          </TabsList>

          {/* 技能管理标签页 */}
          <TabsContent value="skills" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>技能列表</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        <Checkbox
                          checked={selectedUserSkills.length === filteredSkills.length && filteredSkills.length > 0}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedUserSkills(filteredSkills.map(skill => skill.id))
                            } else {
                              setSelectedUserSkills([])
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>技能名称</TableHead>
                      <TableHead>分类</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>掌握人数</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSkills.map((skill) => (
                      <TableRow key={skill.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedUserSkills.includes(skill.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedUserSkills([...selectedUserSkills, skill.id])
                              } else {
                                setSelectedUserSkills(selectedUserSkills.filter(id => id !== skill.id))
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{skill.name}</TableCell>
                        <TableCell>
                          <Badge variant="secondary">
                            {getCategoryLabel(skill.category || '')}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-xs truncate" title={skill.description || undefined}>
                          {skill.description || '暂无描述'}
                        </TableCell>
                        <TableCell>
                          <Badge variant={skill.is_active ? "default" : "destructive"}>
                            {skill.is_active ? "启用" : "禁用"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            0
                          </div>
                        </TableCell>
                        <TableCell>{new Date().toLocaleDateString()}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>操作</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => openEditDialog(skill)}>
                                <Edit2 className="h-4 w-4 mr-2" />
                                编辑
                              </DropdownMenuItem>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <DropdownMenuItem onSelect={(e: Event) => e.preventDefault()}>
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    删除
                                  </DropdownMenuItem>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>确认删除</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      您确定要删除技能"{skill.name}"吗？此操作无法撤销。
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>取消</AlertDialogCancel>
                                    <AlertDialogAction onClick={() => handleDeleteSkill(skill.id)}>
                                      删除
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 员工技能标签页 */}
          <TabsContent value="user-skills" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>员工技能分配</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>员工姓名</TableHead>
                      <TableHead>头像</TableHead>
                      <TableHead>技能名称</TableHead>
                      <TableHead>熟练度</TableHead>
                      <TableHead>认证状态</TableHead>
                      <TableHead>分配时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUserSkills.map((userSkill) => {
                      const proficiencyInfo = getProficiencyInfo(userSkill.proficiency_level)
                      return (
                        <TableRow key={userSkill.id}>
                          <TableCell className="font-medium">{userSkill.user_name}</TableCell>
                          <TableCell>
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${userSkill.user_name}`} />
                              <AvatarFallback>{userSkill.user_name.charAt(0)}</AvatarFallback>
                            </Avatar>
                          </TableCell>
                          <TableCell>{userSkill.skill_name}</TableCell>
                          <TableCell>
                            <Badge className={proficiencyInfo.color}>
                              {proficiencyInfo.label}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {userSkill.is_certified ? (
                              <div className="flex items-center text-green-600">
                                <Award className="h-4 w-4 mr-1" />
                                已认证
                                {userSkill.certified_at && (
                                  <span className="ml-1 text-xs text-muted-foreground">
                                    ({userSkill.certified_at})
                                  </span>
                                )}
                              </div>
                            ) : (
                              <Badge variant="outline">未认证</Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {userSkill.assigned_at}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              {!userSkill.is_certified && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleCertifySkill(userSkill.id)}
                                >
                                  <Award className="h-4 w-4 mr-1" />
                                  认证
                                </Button>
                              )}
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <Edit2 className="h-4 w-4 mr-2" />
                                    修改熟练度
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-red-600">
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    移除技能
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 技能对话框 */}
        <SkillDialog
          open={isSkillDialogOpen}
          onOpenChange={setIsSkillDialogOpen}
          editingSkill={selectedSkill}
          onSubmit={handleSkillSubmit}
          isLoading={createSkillMutation.isPending || updateSkillMutation.isPending}
        />
      </div>
    </AppLayout>
  )
}
