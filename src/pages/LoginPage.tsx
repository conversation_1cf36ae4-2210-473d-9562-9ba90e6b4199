import { useState, useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  generateImageCodeApiV1AuthGenerateImageCodePostMutation, 
  loginWithPhonePasswordApiV1AuthLoginPhonePasswordPostMutation,
  sendSmsCodeApiV1AuthSendSmsCodePostMutation,
  loginWithPhoneSmsApiV1AuthLoginPhoneSmsPostMutation
} from '@/services/@tanstack/react-query.gen'
import type { ImageCodeResponseDto } from '@/services/types.gen'

type LoginMode = 'password' | 'sms'

export default function LoginPage() {
  const [loginMode, setLoginMode] = useState<LoginMode>('password')
  const [phone, setPhone] = useState('')
  const [password, setPassword] = useState('')
  const [phoneError, setPhoneError] = useState('')
  const [smsCode, setSmsCode] = useState('')
  const [imageCode, setImageCode] = useState('')
  const [captchaData, setCaptchaData] = useState<ImageCodeResponseDto | null>(null)
  const [smsCountdown, setSmsCountdown] = useState(0)
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  // 获取URL参数
  const returnUrl = searchParams.get('returnUrl')
  const factoryId = searchParams.get('factory_id')

  // 如果有factory_id参数，保存到localStorage
  useEffect(() => {
    if (factoryId) {
      localStorage.setItem('factory_id', factoryId)
    }
  }, [factoryId])

  // Generate captcha image mutation
  const generateCaptchaMutation = useMutation({
    ...generateImageCodeApiV1AuthGenerateImageCodePostMutation({
        body: {
        }
    }),
    onSuccess: (data) => {
      setCaptchaData(data as ImageCodeResponseDto)
    },
    onError: (error) => {
      console.error('Failed to generate captcha:', error)
    },
  })

  // Send SMS code mutation
  const sendSmsMutation = useMutation({
    ...sendSmsCodeApiV1AuthSendSmsCodePostMutation(),
    onSuccess: () => {
      // Start countdown timer
      setSmsCountdown(60)
      const timer = setInterval(() => {
        setSmsCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    },
    onError: (error) => {
      console.error('Failed to send SMS:', error)
      // Refresh captcha on error
      refreshCaptcha()
    },
  })

  // Password login mutation
  const passwordLoginMutation = useMutation({
    ...loginWithPhonePasswordApiV1AuthLoginPhonePasswordPostMutation(),
    onSuccess: (data) => {
      handleLoginSuccess(data)
    },
    onError: (error) => {
      console.error('Password login failed:', error)
      refreshCaptcha()
      setPhoneError('')
    },
  })

  // SMS login mutation
  const smsLoginMutation = useMutation({
    ...loginWithPhoneSmsApiV1AuthLoginPhoneSmsPostMutation(),
    onSuccess: (data) => {
      handleLoginSuccess(data)
    },
    onError: (error) => {
      console.error('SMS login failed:', error)
      refreshCaptcha()
      setPhoneError('')
    },
  })

  const handleLoginSuccess = (data: unknown) => {
    console.log('Login successful:', data)
    // Store the access token if it exists in the response
    if (data && typeof data === 'object' && 'access_token' in data) {
      const token = (data as Record<string, unknown>).access_token;
      if (typeof token === 'string') {
        localStorage.setItem('access_token', token);
      }
    }
    localStorage.setItem('isAuthenticated', 'true')
    
    // 登录成功后，如果有returnUrl则跳转到指定页面，否则跳转到dashboard
    const redirectUrl = returnUrl || '/dashboard'
    console.log('重定向到:', redirectUrl)
    navigate(redirectUrl)
  }

  // Validate phone number format (Chinese mobile pattern)
  const validatePhone = (phoneNumber: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phoneNumber)
  }

  const handlePhoneChange = (value: string) => {
    setPhone(value)
    if (value && !validatePhone(value)) {
      setPhoneError('请输入正确的手机号格式（以1开头的11位数字）')
    } else {
      setPhoneError('')
    }
  }

  // Generate captcha on component mount
  useEffect(() => {
    refreshCaptcha()
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  const refreshCaptcha = () => {
    generateCaptchaMutation.mutate({
      body: {} // The API doesn't require any body parameters
    })
  }

  const handleSendSms = () => {
    if (!validatePhone(phone)) {
      setPhoneError('请输入正确的手机号格式')
      return
    }
    
    if (phone && imageCode && captchaData) {
      sendSmsMutation.mutate({
        body: {
          phone,
          image_code: imageCode,
          session_id: captchaData.session_id,
        },
      })
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validatePhone(phone)) {
      setPhoneError('请输入正确的手机号格式')
      return
    }
    
    if (loginMode === 'password') {
      if (phone && password && imageCode && captchaData) {
        passwordLoginMutation.mutate({
          body: {
            phone,
            password,
            image_code: imageCode,
            session_id: captchaData.session_id,
          },
        })
      }
    } else {
      if (phone && smsCode && imageCode && captchaData) {
        smsLoginMutation.mutate({
          body: {
            phone,
            sms_code: smsCode,
            image_code: imageCode,
            session_id: captchaData.session_id,
          },
        })
      }
    }
  }

  const isLoading = passwordLoginMutation.isPending || smsLoginMutation.isPending
  const hasError = passwordLoginMutation.isError || smsLoginMutation.isError || generateCaptchaMutation.isError

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">扫非系统登录</CardTitle>
          <CardDescription className="text-center">
            服装扫码计件系统
          </CardDescription>
          <div className="text-xs text-gray-500 text-center mt-2">
            请使用中国大陆手机号登录（以1开头的11位数字）
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={loginMode} onValueChange={(value: string) => setLoginMode(value as LoginMode)} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="password">密码登录</TabsTrigger>
              <TabsTrigger value="sms">短信登录</TabsTrigger>
            </TabsList>
            
            <TabsContent value="password" className="space-y-4 mt-4">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">手机号</Label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    required
                    placeholder="请输入手机号（如：13800138000）"
                    value={phone}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handlePhoneChange(e.target.value)}
                    disabled={isLoading}
                    className={phoneError ? 'border-red-500' : ''}
                  />
                  {phoneError && (
                    <p className="text-sm text-red-600">{phoneError}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">密码</Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    required
                    placeholder="请输入密码"
                    value={password}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
                    disabled={isLoading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="imageCode">验证码</Label>
                  <div className="flex gap-2">
                    <Input
                      id="imageCode"
                      name="imageCode"
                      type="text"
                      required
                      placeholder="请输入验证码"
                      value={imageCode}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setImageCode(e.target.value)}
                      disabled={isLoading}
                      className="flex-1"
                    />
                    <div className="flex flex-col gap-1">
                      {captchaData?.image_base64 ? (
                        <img
                          src={captchaData.image_base64}
                          alt="验证码"
                          className="w-24 h-10 border rounded cursor-pointer"
                          onClick={refreshCaptcha}
                          title="点击刷新验证码"
                        />
                      ) : (
                        <div className="w-24 h-10 border rounded flex items-center justify-center bg-gray-100">
                          <span className="text-xs text-gray-500">加载中...</span>
                        </div>
                      )}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={refreshCaptcha}
                        disabled={generateCaptchaMutation.isPending}
                        className="text-xs h-6"
                      >
                        刷新
                      </Button>
                    </div>
                  </div>
                </div>

                {hasError && (
                  <div className="text-sm text-red-600 text-center space-y-1">
                    {passwordLoginMutation.isError && (
                      <div>密码登录失败，请检查手机号、密码和验证码是否正确</div>
                    )}
                    {generateCaptchaMutation.isError && (
                      <div>验证码加载失败，请刷新重试</div>
                    )}
                  </div>
                )}

                <Button type="submit" className="w-full" disabled={isLoading || !captchaData}>
                  {passwordLoginMutation.isPending ? '登录中...' : '密码登录'}
                </Button>
              </form>
            </TabsContent>
            
            <TabsContent value="sms" className="space-y-4 mt-4">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="phone-sms">手机号</Label>
                  <Input
                    id="phone-sms"
                    name="phone-sms"
                    type="tel"
                    required
                    placeholder="请输入手机号（如：13800138000）"
                    value={phone}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handlePhoneChange(e.target.value)}
                    disabled={isLoading}
                    className={phoneError ? 'border-red-500' : ''}
                  />
                  {phoneError && (
                    <p className="text-sm text-red-600">{phoneError}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="imageCode-sms">验证码</Label>
                  <div className="flex gap-2">
                    <Input
                      id="imageCode-sms"
                      name="imageCode-sms"
                      type="text"
                      required
                      placeholder="请输入图片验证码"
                      value={imageCode}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setImageCode(e.target.value)}
                      disabled={isLoading}
                      className="flex-1"
                    />
                    <div className="flex flex-col gap-1">
                      {captchaData?.image_base64 ? (
                        <img
                          src={captchaData.image_base64}
                          alt="验证码"
                          className="w-24 h-10 border rounded cursor-pointer"
                          onClick={refreshCaptcha}
                          title="点击刷新验证码"
                        />
                      ) : (
                        <div className="w-24 h-10 border rounded flex items-center justify-center bg-gray-100">
                          <span className="text-xs text-gray-500">加载中...</span>
                        </div>
                      )}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={refreshCaptcha}
                        disabled={generateCaptchaMutation.isPending}
                        className="text-xs h-6"
                      >
                        刷新
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="smsCode">短信验证码</Label>
                  <div className="flex gap-2">
                    <Input
                      id="smsCode"
                      name="smsCode"
                      type="text"
                      required
                      placeholder="请输入短信验证码"
                      value={smsCode}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSmsCode(e.target.value)}
                      disabled={isLoading}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleSendSms}
                      disabled={sendSmsMutation.isPending || smsCountdown > 0 || !validatePhone(phone) || !imageCode || !captchaData}
                      className="w-24"
                    >
                      {sendSmsMutation.isPending 
                        ? '发送中...' 
                        : smsCountdown > 0 
                        ? `${smsCountdown}s` 
                        : '发送验证码'
                      }
                    </Button>
                  </div>
                </div>

                {hasError && (
                  <div className="text-sm text-red-600 text-center space-y-1">
                    {smsLoginMutation.isError && (
                      <div>短信登录失败，请检查手机号和验证码是否正确</div>
                    )}
                    {sendSmsMutation.isError && (
                      <div>短信发送失败，请检查手机号和图片验证码</div>
                    )}
                    {generateCaptchaMutation.isError && (
                      <div>验证码加载失败，请刷新重试</div>
                    )}
                  </div>
                )}

                <Button type="submit" className="w-full" disabled={isLoading || !captchaData}>
                  {smsLoginMutation.isPending ? '登录中...' : '短信登录'}
                </Button>
              </form>
            </TabsContent>
          </Tabs>

          <div className="flex items-center justify-between mt-6">
            <div className="flex items-center space-x-2">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-ring"
              />
              <label htmlFor="remember-me" className="text-sm text-muted-foreground">
                记住我
              </label>
            </div>

            <div className="text-sm">
              <a href="#" className="font-medium text-primary hover:underline">
                忘记密码？
              </a>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
