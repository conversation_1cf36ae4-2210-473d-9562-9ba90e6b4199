import React, { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { getOrderByOrderNoMainApiV1OrdersOrderNoGetOptions, getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetOptions } from '@/services/@tanstack/react-query.gen'
import AppLayout from '@/components/AppLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Package, Target } from 'lucide-react'
import { OrderPartsAndBundles } from '@/components/order/OrderPartsAndBundles'
import { OrderCraftSteps, OrderCraftsEdit } from '@/components/order'

const OrderDetailPage: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>()
  const navigate = useNavigate()
  const [isEditingCrafts, setIsEditingCrafts] = useState(false)

  const { data: order, isLoading, error } = useQuery({
    ...getOrderByOrderNoMainApiV1OrdersOrderNoGetOptions({ 
      path: { order_no: orderId! }
    }),
    enabled: !!orderId
  })

  // 单独获取订单工艺数据
  const { data: orderCrafts, isLoading: isCraftsLoading } = useQuery({
    ...getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetOptions({
      path: { order_no: orderId! }
    }),
    enabled: !!orderId
  })

  // 获取状态标志样式
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'pending': { label: '待处理', variant: 'secondary' as const },
      'in_progress': { label: '进行中', variant: 'default' as const },
      'completed': { label: '已完成', variant: 'outline' as const },
      'cancelled': { label: '已取消', variant: 'destructive' as const }
    }
    
    return statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'outline' as const }
  }

  // 格式化日期
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  // 格式化价格
  const formatPrice = (price?: string | null) => {
    if (!price) return '-'
    const numPrice = parseFloat(price)
    return isNaN(numPrice) ? price : `¥${numPrice.toFixed(2)}`
  }

  const handleBack = () => {
    navigate('/orders')
  }

  if (isLoading || isCraftsLoading) {
    return (
      <AppLayout>
        <div className="container mx-auto py-6">
          <div className="text-center py-8">
            <p className="text-muted-foreground">加载中...</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  if (error || !order) {
    return (
      <AppLayout>
        <div className="container mx-auto py-6">
          <div className="text-center py-8">
            <p className="text-red-500">订单加载失败</p>
            <Button className="mt-4" onClick={handleBack}>
              返回订单列表
            </Button>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="container mx-auto py-6">
        {/* 头部操作区 */}
        <div className="mb-6 flex justify-between items-start">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={handleBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">订单详情</h1>
              <p className="text-muted-foreground mt-1">
                订单号: {order.order_no}
              </p>
            </div>
          </div>
        </div>

        {/* 基本信息 - 紧凑展示 */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Package className="w-5 h-5" />
              基本信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 text-sm">
              <div className="space-y-1">
                <p className="text-muted-foreground">订单号</p>
                <p className="font-medium">{order.order_no}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">SKC号</p>
                <p className="font-medium">{order.skc_no || '-'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">外部订单号</p>
                <p className="font-medium">{order.external_order_no || '-'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">状态</p>
                <Badge variant={getStatusBadge(order.status).variant}>
                  {getStatusBadge(order.status).label}
                </Badge>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">成本</p>
                <p className="font-medium">{formatPrice(order.cost)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">价格</p>
                <p className="font-medium">{formatPrice(order.price)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">创建时间</p>
                <p className="font-medium">{formatDate(order.created_at)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">交货时间</p>
                <p className="font-medium">{formatDate(order.expect_finished_at)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">负责人</p>
                <p className="font-medium">{order.owner_user_id || '-'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">总数量</p>
                <p className="font-medium">{order.order_lines?.reduce((sum, line) => sum + line.amount, 0) || 0}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">尺码数</p>
                <p className="font-medium">{order.order_lines?.length || 0}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">工艺步骤</p>
                <p className="font-medium">{orderCrafts?.length || 0}</p>
              </div>
            </div>
            {order.description && (
              <div className="mt-4 pt-4 border-t">
                <p className="text-muted-foreground text-sm mb-1">描述</p>
                <p className="text-sm">{order.description}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 工艺步骤 / 工艺路线配置 */}
        {(orderCrafts && orderCrafts.length > 0) || isEditingCrafts ? (
          isEditingCrafts ? (
            <div className="mb-6">
              <OrderCraftsEdit
                orderNo={order.order_no}
                orderCrafts={orderCrafts || []}
                extraButtons={
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditingCrafts(false)}
                  >
                    切换到步骤视图
                  </Button>
                }
              />
            </div>
          ) : (
            <div className="mb-6">
              <OrderCraftSteps
                orderCrafts={orderCrafts || []}
                orderNo={orderId}
                showEditButton
                onShowEdit={() => setIsEditingCrafts(true)}
              />
            </div>
          )
        ) : (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                工艺路线配置
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">暂无工艺配置</p>
                <Button onClick={() => setIsEditingCrafts(true)}>
                  <Target className="w-4 h-4 mr-2" />
                  配置工艺路线
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 床/扎配置 */}
        <OrderPartsAndBundles
          orderLines={order.order_lines || []}
          orderNo={order.order_no}
          skcNo={order.skc_no || order.order_no}
          editable={true}
        />
      </div>
    </AppLayout>
  )
}

export default OrderDetailPage