import React from 'react'
import { useForm } from 'react-hook-form'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent } from "@/components/ui/card"
import { getUsersApiV1UsersGetOptions, getAvailableRegistrationDataOptions, createCraftInstanceApiV1CraftInstancesPostMutation } from '@/services/@tanstack/react-query.gen'
import type { OrderCraftRouteResponseDto, CraftInstanceCreateDto, CompletionGranularityDto } from '@/services/types.gen'
import { toast } from 'sonner'

// 登记类型常量
const REGISTRATION_TYPES = [
  { value: 'ALL', label: '整单录入'},
  { value: 'PART', label: '分床录入'},
  { value: 'BUNDLER', label: '分扎录入'}
]

// 登记表单类型
export interface RegistrationFormData {
  registrationType: string // 登记类型: ALL, PART, BUNDLER
  unitIdentifier: string // 根据登记类型不同：整单号、床号、扎号
  selectedParts: string[] // 选中的分床号
  selectedBundles: string[] // 选中的分扎号
  completionPerson: string // 完成人ID
  startTime: string // 开始时间
  completionTime: string // 完成时间
  completedQuantity: number // 完成数量
  qualityLevel: string // 质量等级
  notes: string // 备注
}

interface RegistrationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedRoute?: OrderCraftRouteResponseDto
  orderNo?: string // 订单号，用于获取分床和分扎数据
  onSubmit: () => Promise<void>
  onCancel: () => void
}

export const RegistrationDialog: React.FC<RegistrationDialogProps> = ({
  open,
  onOpenChange,
  selectedRoute,
  orderNo,
  onSubmit,
  onCancel
}) => {
  const queryClient = useQueryClient()

  // React Hook Form 设置
  const form = useForm<RegistrationFormData>({
    defaultValues: {
      registrationType: '',
      unitIdentifier: '',
      selectedParts: [],
      selectedBundles: [],
      completionPerson: '',
      startTime: '',
      completionTime: '',
      completedQuantity: 1,
      qualityLevel: 'A',
      notes: '',
    }
  })

  const { control, handleSubmit, watch, setValue } = form
  const watchedRegistrationType = watch('registrationType')

  // 创建工艺实例的 mutation
  const createCraftInstanceMutation = useMutation({
    mutationFn: createCraftInstanceApiV1CraftInstancesPostMutation().mutationFn,
    onSuccess: () => {
      toast.success('工序登记成功')
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey
          return queryKey.length > 0 && 
                 typeof queryKey[0] === 'object' &&
                 queryKey[0] !== null &&
                 JSON.stringify(queryKey).includes('CraftInstance') ||
                 JSON.stringify(queryKey).includes('OrderCraft')
        }
      })
      onSubmit()
      onOpenChange(false)
    },
    onError: (error: Error) => {
      toast.error('登记失败: ' + (error.message || '未知错误'))
    }
  })

  // 获取用户列表
  const { data: usersData } = useQuery({
    ...getUsersApiV1UsersGetOptions()
  })

  const employees = usersData?.map((user) => ({
    id: user.id,
    name: user.full_name || user.username,
    code: user.username
  })) || []

  // 获取可用的登记数据（包含分床和分扎）
  const { data: availableRegistrationData } = useQuery({
    ...getAvailableRegistrationDataOptions({
      path: { order_no: orderNo || '' },
      query: {
        craft_route_id: selectedRoute?.id,
        include_completed_routes: false,
        granularity_filter: watchedRegistrationType === 'PART' ? 'part' : watchedRegistrationType === 'BUNDLER' ? 'bundle' : 'order'
      }
    }),
    enabled: !!orderNo && !!selectedRoute?.id && (watchedRegistrationType === 'PART' || watchedRegistrationType === 'BUNDLER')
  })

  // 从可用登记数据中提取分床和分扎信息
  const orderParts = availableRegistrationData?.order_parts || []
  const orderBundles = orderParts.flatMap(part => 
    (part.order_bundles || []).map(bundle => ({
      ...bundle,
      order_part_no: part.order_part_no // 添加所属床号信息
    }))
  ) || []

  // 表单提交处理
  const onFormSubmit = (data: RegistrationFormData) => {
    if (!selectedRoute || !orderNo) {
      toast.error('缺少必要的工序或订单信息')
      return
    }

    // 验证选择的内容
    if (data.registrationType === 'PART' && data.selectedParts.length === 0) {
      toast.error('请至少选择一个分床')
      return
    }

    if (data.registrationType === 'BUNDLER' && data.selectedBundles.length === 0) {
      toast.error('请至少选择一个分扎')
      return
    }

    // 确定完成粒度
    let completionGranularity: CompletionGranularityDto
    if (data.registrationType === 'ALL') {
      completionGranularity = 'order'
    } else if (data.registrationType === 'PART') {
      completionGranularity = 'bed'
    } else {
      completionGranularity = 'bundle'
    }

    // 根据登记类型创建相应的工艺实例
    const promises: Promise<unknown>[] = []

    if (data.registrationType === 'ALL') {
      // 整单登记 - 创建一个工艺实例
      const craftInstanceData: CraftInstanceCreateDto = {
        order_craft_route_id: selectedRoute.id,
        completion_granularity: completionGranularity,
        order_no: orderNo,
        worker_user_id: parseInt(data.completionPerson),
        completed_quantity: data.completedQuantity,
        quality_level: data.qualityLevel,
        started_at: data.startTime,
        notes: data.notes,
      }
      
      promises.push(
        createCraftInstanceMutation.mutateAsync({
          body: craftInstanceData
        })
      )
    } else if (data.registrationType === 'PART') {
      // 分床登记 - 创建一个工艺实例，包含所有选中的床号
      const craftInstanceData: CraftInstanceCreateDto = {
        order_craft_route_id: selectedRoute.id,
        completion_granularity: completionGranularity,
        order_no: orderNo,
        order_part_nos: data.selectedParts,
        worker_user_id: parseInt(data.completionPerson),
        completed_quantity: data.completedQuantity,
        quality_level: data.qualityLevel,
        started_at: data.startTime,
        notes: data.notes,
      }
      
      promises.push(
        createCraftInstanceMutation.mutateAsync({
          body: craftInstanceData
        })
      )
    } else if (data.registrationType === 'BUNDLER') {
      // 分扎登记 - 创建一个工艺实例，包含所有选中的扎号
      const craftInstanceData: CraftInstanceCreateDto = {
        order_craft_route_id: selectedRoute.id,
        completion_granularity: completionGranularity,
        order_no: orderNo,
        order_bundle_nos: data.selectedBundles,
        worker_user_id: parseInt(data.completionPerson),
        completed_quantity: data.completedQuantity,
        quality_level: data.qualityLevel,
        started_at: data.startTime,
        notes: data.notes,
      }
      
      promises.push(
        createCraftInstanceMutation.mutateAsync({
          body: craftInstanceData
        })
      )
    }

    // 执行所有请求
    Promise.all(promises)
      .then(() => {
        // 成功处理在 mutation 的 onSuccess 中
      })
      .catch(() => {
        // 错误处理在 mutation 的 onError 中
      })
  }

  // 处理分床选择
  const handlePartSelection = (orderPartNo: string, checked: boolean) => {
    const currentSelected = form.getValues('selectedParts')
    if (checked) {
      setValue('selectedParts', [...currentSelected, orderPartNo])
    } else {
      setValue('selectedParts', currentSelected.filter(p => p !== orderPartNo))
    }
  }

  // 处理分扎选择
  const handleBundleSelection = (orderBundleNo: string, checked: boolean) => {
    const currentSelected = form.getValues('selectedBundles')
    if (checked) {
      setValue('selectedBundles', [...currentSelected, orderBundleNo])
    } else {
      setValue('selectedBundles', currentSelected.filter(b => b !== orderBundleNo))
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>工序登记</DialogTitle>
          <DialogDescription>
            请填写工序完成信息
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 overflow-y-auto py-4">
          <Form {...form}>
            <form id="registration-form" onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
              {/* 显示选中的工序信息 */}
              {selectedRoute && (
                <div className="p-3 bg-muted/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-sm">选中工序:</span>
                    <span className="text-sm">{selectedRoute.skill_name || selectedRoute.skill_code}</span>
                    <Badge variant="outline" className="text-xs">{selectedRoute.skill_code}</Badge>
                  </div>
                </div>
              )}

              {/* 完成人和开始时间 */}
              <div className="grid grid-cols-2 gap-6">
                <FormField
                  control={control}
                  name="completionPerson"
                  rules={{ required: '请选择完成人' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>完成人</FormLabel>
                      <FormControl>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="选择完成人" />
                          </SelectTrigger>
                          <SelectContent>
                            {employees.map((employee) => (
                              <SelectItem key={employee.id} value={employee.id.toString()}>
                                {employee.name} ({employee.code})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="startTime"
                  rules={{ required: '请选择开始时间' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>开始时间</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

            {/* 完成数量和质量等级 */}
            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={control}
                name="completedQuantity"
                rules={{ required: '请输入完成数量', min: { value: 1, message: '完成数量必须大于0' } }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>完成数量</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="qualityLevel"
                rules={{ required: '请选择质量等级' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>质量等级</FormLabel>
                    <FormControl>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择质量等级" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="A">A级 (优秀)</SelectItem>
                          <SelectItem value="B">B级 (良好)</SelectItem>
                          <SelectItem value="C">C级 (合格)</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>                  )}
                />
              </div>

              {/* 备注 */}
              <FormField
                control={control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>备注</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="输入备注信息（可选）"
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

            {/* 登记类型 - 使用Radio按钮，放在最下面 */}
            <FormField
              control={control}
              name="registrationType"
              rules={{ required: '请选择登记类型' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>登记类型</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-3 gap-4">
                      {selectedRoute?.registration_types?.map((type) => {
                        const typeConfig = REGISTRATION_TYPES.find(t => t.value === type)
                        if (!typeConfig) return null
                        
                        return (
                          <div key={type} className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id={`registration-type-${type}`}
                              name="registrationType"
                              value={type}
                              checked={field.value === type}
                              onChange={() => {
                                field.onChange(type)
                                // 清空相关字段
                                setValue('unitIdentifier', '')
                                setValue('selectedParts', [])
                                setValue('selectedBundles', [])
                              }}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                            />
                            <Label htmlFor={`registration-type-${type}`} className="text-sm font-normal cursor-pointer">
                              {typeConfig.label}
                            </Label>
                          </div>
                        )
                      })}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 分床选择卡片 */}
            {watchedRegistrationType === 'PART' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">选择分床</h3>
                <p className="text-sm text-muted-foreground">请选择要登记完成的分床，可以选择多个。</p>
                {orderParts.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>当前订单暂无可登记的分床数据</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {orderParts.map((part) => {
                    const isSelected = form.watch('selectedParts').includes(part.order_part_no || '')
                    const availableQuantity = part.available_quantity || 0
                    const totalQuantity = part.total_quantity || 0
                    const registeredQuantity = part.registered_quantity || 0
                    
                    return (
                      <Card 
                        key={part.order_part_no} 
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          isSelected ? 'ring-2 ring-primary bg-primary/5' : 'hover:border-primary/50'
                        }`}
                        onClick={() => handlePartSelection(part.order_part_no || '', !isSelected)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <Checkbox
                                checked={isSelected}
                                onCheckedChange={(checked) => 
                                  handlePartSelection(part.order_part_no || '', checked as boolean)
                                }
                                onClick={(e) => e.stopPropagation()}
                              />
                              <span className="font-medium text-lg">{part.order_part_no}</span>
                            </div>
                            <Badge variant={availableQuantity > 0 ? 'default' : 'secondary'}>
                              {availableQuantity > 0 ? '可登记' : '已完成'}
                            </Badge>
                          </div>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">总数量:</span>
                              <span className="font-medium">{totalQuantity}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">已登记:</span>
                              <span className="font-medium">{registeredQuantity}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">可登记:</span>
                              <span className="font-medium text-primary">{availableQuantity}</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                  </div>
                )}
              </div>
            )}

            {/* 分扎选择卡片 */}
            {watchedRegistrationType === 'BUNDLER' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">选择分扎</h3>
                <p className="text-sm text-muted-foreground">请选择要登记完成的分扎，可以选择多个。扎号已简化显示（显示后6位）。</p>
                {orderBundles.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>当前订单暂无可登记的分扎数据</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {orderBundles.map((bundle) => {
                    const isSelected = form.watch('selectedBundles').includes(bundle.order_bundle_no || '')
                    const availableQuantity = bundle.available_quantity || 0
                    const totalQuantity = bundle.total_quantity || 0
                    const registeredQuantity = bundle.registered_quantity || 0
                    
                    // 只显示扎号的后6位
                    const displayBundleNo = bundle.order_bundle_no ? 
                      (bundle.order_bundle_no.length > 6 ? 
                        '...' + bundle.order_bundle_no.slice(-6) : 
                        bundle.order_bundle_no) : ''
                    
                    return (
                      <Card 
                        key={bundle.order_bundle_no} 
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          isSelected ? 'ring-2 ring-primary bg-primary/5' : 'hover:border-primary/50'
                        }`}
                        onClick={() => handleBundleSelection(bundle.order_bundle_no || '', !isSelected)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <Checkbox
                                checked={isSelected}
                                onCheckedChange={(checked) => 
                                  handleBundleSelection(bundle.order_bundle_no || '', checked as boolean)
                                }
                                onClick={(e) => e.stopPropagation()}
                              />
                              <span className="font-medium text-lg" title={bundle.order_bundle_no}>
                                {displayBundleNo}
                              </span>
                            </div>
                            <Badge variant={availableQuantity > 0 ? 'default' : 'secondary'}>
                              {availableQuantity > 0 ? '可登记' : '已完成'}
                            </Badge>
                          </div>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">总数量:</span>
                              <span className="font-medium">{totalQuantity}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">已登记:</span>
                              <span className="font-medium">{registeredQuantity}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">可登记:</span>
                              <span className="font-medium text-primary">{availableQuantity}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">所属床号:</span>
                              <span className="font-medium text-xs">{bundle.order_part_no || '-'}</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                  </div>
                )}
              </div>
            )}

            </form>
          </Form>
        </div>

        <DialogFooter className="flex-shrink-0 border-t bg-background p-6">
          <Button type="button" variant="outline" onClick={onCancel} disabled={createCraftInstanceMutation.isPending}>
            取消
          </Button>
          <Button type="submit" form="registration-form" disabled={createCraftInstanceMutation.isPending}>
            {createCraftInstanceMutation.isPending ? '提交中...' : '提交登记'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
