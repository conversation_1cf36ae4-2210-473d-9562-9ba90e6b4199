import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'

interface OrderBaseInfoProps {
  employees?: Array<{ id: number; name: string }>
}

const OrderBaseInfo: React.FC<OrderBaseInfoProps> = ({ 
  employees = [] 
}) => {
  const { control } = useFormContext()
  return (
    <Card>
      <CardHeader>
        <CardTitle>基础信息</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* SKC 号 */}
          <FormField
            control={control}
            name="skc_no"
            rules={{ required: "SKC号是必填项" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">
                  SKC号 <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="请输入SKC号"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 外部 SKC 号 */}
          <FormField
            control={control}
            name="external_skc_no"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">
                  外部SKC号
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="请输入外部SKC号"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 订单号 */}
          <FormField
            control={control}
            name="order_no"
            rules={{ required: "订单号是必填项" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">
                  订单号 <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="请输入订单号"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 外部订单号 */}
          <FormField
            control={control}
            name="external_order_no"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">
                  外部订单号
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="请输入外部订单号"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 外部订单号2 */}
          <FormField
            control={control}
            name="external_order_no2"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">
                  外部订单号2
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="请输入外部订单号2"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 价格 */}
          <FormField
            control={control}
            name="price"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">
                  价格 (元)
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="请输入价格"
                    onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : '')}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 预期完成时间 */}
          <FormField
            control={control}
            name="expect_finished_at"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">
                  预期完成时间
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="datetime-local"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 负责人 */}
          <FormField
            control={control}
            name="owner_user_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">
                  负责人
                </FormLabel>
                <FormControl>
                  <Select 
                    value={field.value?.toString() || ''} 
                    onValueChange={(value) => field.onChange(value ? parseInt(value) : null)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="请选择负责人" />
                    </SelectTrigger>
                    <SelectContent>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id.toString()}>
                          {employee.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* 备注 */}
        <FormField
          control={control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium">
                订单备注
              </FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="请输入订单备注信息"
                  rows={3}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  )
}

export default OrderBaseInfo
