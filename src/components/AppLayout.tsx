import { Link, useNavigate, useLocation } from 'react-router-dom'
import { useState, useRef, useEffect } from 'react'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/simple-sidebar"
import { Home, Users, Building2, LogOut, ChevronDown, Factory, Award, Route, UserCog, Plus } from "lucide-react"

function SidebarHeaderContent({ 
  selectedFactory, 
  setSelectedFactory 
}: {
  selectedFactory: string
  setSelectedFactory: (factory: string) => void
}) {
  const { open } = useSidebar()
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  
  const factories = [
    { id: 1, name: "工厂A", location: "广州" },
    { id: 2, name: "工厂B", location: "深圳" },
    { id: 3, name: "工厂C", location: "东莞" },
  ]

  const handleFactoryChange = (factoryName: string) => {
    setSelectedFactory(factoryName)
    setIsDropdownOpen(false)
  }

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])
  
  return (
    <div className="relative" ref={dropdownRef}>
      <div 
        className="flex items-center gap-2 px-2 py-2 cursor-pointer hover:bg-accent rounded-md"
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
      >
        <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
          <Factory className="size-4" />
        </div>
        {open && (
          <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col gap-0.5 leading-none">
              <span className="font-semibold">{selectedFactory}</span>
              <span className="text-xs text-muted-foreground">点击切换工厂</span>
            </div>
            <ChevronDown className="size-4 text-muted-foreground" />
          </div>
        )}
      </div>
      
      {/* Dropdown Menu */}
      {isDropdownOpen && open && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-lg z-50">
          {factories.map((factory) => (
            <div
              key={factory.id}
              className="flex items-center gap-2 px-3 py-2 hover:bg-accent cursor-pointer"
              onClick={() => handleFactoryChange(factory.name)}
            >
              <Factory className="size-4 text-muted-foreground" />
              <div className="flex flex-col">
                <span className="text-sm font-medium">{factory.name}</span>
                <span className="text-xs text-muted-foreground">{factory.location}</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

interface AppLayoutProps {
  children: React.ReactNode
}

export default function AppLayout({ children }: AppLayoutProps) {
  const navigate = useNavigate()
  const location = useLocation()
  const [selectedFactory, setSelectedFactory] = useState("工厂A")

  const handleLogout = () => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('isAuthenticated')
    navigate('/login')
  }

  const menuItems = [
    {
      title: "仪表板",
      url: "/dashboard",
      icon: Home,
    },
    {
      title: "新建订单",
      url: "/orders/new",
      icon: Plus,
    },
    {
      title: '订单列表',
      url: '/orders',
      icon: Plus,
    },
    {
      title: "部门管理",
      url: "/departments",
      icon: Building2,
    },
    {
      title: "员工管理",
      url: "/employees",
      icon: Users,
    },
    {
      title: "角色管理",
      url: "/roles",
      icon: UserCog,
    },
    {
      title: "技能管理",
      url: "/skills",
      icon: Award,
    },
    {
      title: "工艺路线",
      url: "/crafts",
      icon: Route,
    },

  ]

  return (
    <SidebarProvider>
      <Sidebar>
        <SidebarHeader>
          <SidebarHeaderContent 
            selectedFactory={selectedFactory}
            setSelectedFactory={setSelectedFactory}
          />
        </SidebarHeader>
        
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>主要功能</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {menuItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <Link to={item.url}>
                      <SidebarMenuButton 
                        isActive={location.pathname === item.url}
                      >
                        <item.icon className="w-4 h-4" />
                        <span>{item.title}</span>
                      </SidebarMenuButton>
                    </Link>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton onClick={handleLogout}>
                <LogOut className="w-4 h-4" />
                <span>退出登录</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>
      
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <div className="flex flex-1 items-center gap-2 px-2">
            <h1 className="text-lg font-semibold">当前工厂: {selectedFactory}</h1>
          </div>
        </header>
        <main className="flex flex-1 flex-col gap-4 p-4">
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
