<!-- Use this file to provide workspace-specific custom instructions to Copilot. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# 扫非系统 (ScanPay System) 开发指导

这是一个基于React + TypeScript + Vite + Tailwind CSS + React Router的服装扫码计件/价系统Web端项目。

## 项目结构
- `/src/pages/` - 页面组件
- `/src/components/` - 公共组件
- `/src/lib/` - 工具函数库
- `/src/assets/` - 静态资源

## 技术栈
- React 18 + TypeScript
- Vite 构建工具
- Tailwind CSS 样式框架
- React Router 路由管理
- shadcn/ui 组件库架构

## 开发规范
1. 使用TypeScript进行类型安全开发
2. 采用Tailwind CSS进行样式编写
3. 遵循React函数组件和Hooks最佳实践
4. 使用clsx和tailwind-merge处理CSS类名
5. 保持中文界面和注释

## 核心功能模块
- 用户登录认证
- 部门管理（增删改查）
- 员工管理（增删改查）
- 权限控制和路由保护

## 代码风格
- 优先使用函数组件和React Hooks
- 使用TypeScript interface定义数据类型
- CSS类名使用Tailwind工具类
- 组件命名使用PascalCase
- 文件命名使用PascalCase.tsx

## UI设计原则
- 现代化、简洁的界面设计
- 响应式布局支持
- 良好的用户体验
- 统一的视觉风格
